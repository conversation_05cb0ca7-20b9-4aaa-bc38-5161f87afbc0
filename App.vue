<template>
  <div id="app">
    <div class="container">
      <h1 class="title">🏥 健康报告助手</h1>
      <p class="subtitle">Vue3 测试页面</p>

      <div class="test-section">
        <p class="count">计数: {{ count }}</p>
        <button @click="increment" class="btn">点击 +1</button>
      </div>

      <div class="nav-section">
        <button @click="showMessage" class="btn secondary">显示消息</button>
        <button @click="testStorage" class="btn secondary">测试存储</button>
      </div>

      <p class="status">{{ status }}</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'App',
  setup() {
    const count = ref(0)
    const status = ref('Vue3 已加载')

    onMounted(() => {
      console.log('App mounted')
      status.value = 'Vue3 页面加载成功 ✅'
    })

    const increment = () => {
      count.value++
      console.log('Count:', count.value)
      status.value = `计数器工作正常，当前值: ${count.value}`
    }

    const showMessage = () => {
      alert('Hello Vue3!')
      status.value = '消息显示正常 ✅'
    }

    const testStorage = () => {
      try {
        localStorage.setItem('test', 'Vue3 Storage Test')
        const value = localStorage.getItem('test')
        status.value = `存储测试成功: ${value}`
        alert('存储测试成功')
      } catch (error) {
        status.value = '存储测试失败: ' + error.message
        console.error('Storage test failed:', error)
      }
    }

    return {
      count,
      status,
      increment,
      showMessage,
      testStorage
    }
  }
}
</script>

<style lang="scss">
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
  padding: 30px 20px;
}

.container {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.title {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-bottom: 2rem;
}

.test-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.count {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.btn {
  background: white;
  color: #667eea;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  margin: 5px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.nav-section {
  margin-bottom: 2rem;
}

.status {
  font-size: 1rem;
  opacity: 0.8;
  margin-top: 2rem;
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #f8f8f8;
  font-size: 16px;
  line-height: 1.6;
}
</style>
