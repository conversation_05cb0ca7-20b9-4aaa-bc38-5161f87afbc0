const path = require('path')

module.exports = {
  // 基础路径
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  
  // 输出目录
  outputDir: 'dist',
  
  // 静态资源目录
  assetsDir: 'static',
  
  // 是否在保存时进行eslint检查
  lintOnSave: process.env.NODE_ENV !== 'production',
  
  // 生产环境source map
  productionSourceMap: false,
  
  // webpack配置
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './'),
        '@/components': path.resolve(__dirname, './components'),
        '@/pages': path.resolve(__dirname, './pages'),
        '@/utils': path.resolve(__dirname, './utils'),
        '@/stores': path.resolve(__dirname, './stores'),
        '@/services': path.resolve(__dirname, './services'),
        '@/styles': path.resolve(__dirname, './styles'),
        '@/static': path.resolve(__dirname, './static')
      }
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'all',
            priority: 10
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true
          }
        }
      }
    }
  },
  
  // CSS配置
  css: {
    loaderOptions: {
      sass: {
        additionalData: `@import "@/styles/uni.scss";`
      }
    }
  },
  
  // 开发服务器配置
  devServer: {
    port: 8080,
    host: '0.0.0.0',
    https: false,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  },
  
  // 第三方插件配置
  pluginOptions: {
    'uni-app': {
      // uni-app 特定配置
    }
  }
}
