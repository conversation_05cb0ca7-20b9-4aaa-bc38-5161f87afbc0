<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康报告助手 - 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 30px 20px;
        }

        .container {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-bottom: 2rem;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .count {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .btn {
            background: white;
            color: #667eea;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 1rem;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .btn.secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn.secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .status {
            font-size: 1rem;
            opacity: 0.8;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1 class="title">🏥 健康报告助手</h1>
            <p class="subtitle">Vue3 测试页面</p>

            <div class="test-section">
                <p class="count">计数: <span id="count">0</span></p>
                <button onclick="increment()" class="btn">点击 +1</button>
            </div>

            <div class="nav-section">
                <button onclick="showMessage()" class="btn secondary">显示消息</button>
                <button onclick="testStorage()" class="btn secondary">测试存储</button>
            </div>

            <p class="status" id="status">页面加载成功 ✅</p>
        </div>
    </div>

    <script>
        let count = 0;

        function increment() {
            count++;
            document.getElementById('count').textContent = count;
            document.getElementById('status').textContent = `计数器工作正常，当前值: ${count}`;
            console.log('Count:', count);
        }

        function showMessage() {
            alert('Hello Vue3!');
            document.getElementById('status').textContent = '消息显示正常 ✅';
        }

        function testStorage() {
            try {
                localStorage.setItem('test', 'Vue3 Storage Test');
                const value = localStorage.getItem('test');
                document.getElementById('status').textContent = `存储测试成功: ${value}`;
                alert('存储测试成功');
            } catch (error) {
                document.getElementById('status').textContent = '存储测试失败: ' + error.message;
                console.error('Storage test failed:', error);
            }
        }

        console.log('页面加载完成');
    </script>
</body>
</html>
