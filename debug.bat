@echo off
echo ================================
echo 健康报告助手 - 问题诊断脚本
echo ================================
echo.

echo 1. 检查关键文件是否存在...
if exist "index.html" (
    echo ✅ index.html 存在
) else (
    echo ❌ index.html 不存在
)

if exist "main.js" (
    echo ✅ main.js 存在
) else (
    echo ❌ main.js 不存在
)

if exist "App.vue" (
    echo ✅ App.vue 存在
) else (
    echo ❌ App.vue 不存在
)

if exist "pages.json" (
    echo ✅ pages.json 存在
) else (
    echo ❌ pages.json 不存在
)

if exist "manifest.json" (
    echo ✅ manifest.json 存在
) else (
    echo ❌ manifest.json 不存在
)

echo.
echo 2. 检查Vue3配置文件...
if exist "vue.config.js" (
    echo ✅ vue.config.js 存在
) else (
    echo ❌ vue.config.js 不存在
)

if exist "babel.config.js" (
    echo ✅ babel.config.js 存在
) else (
    echo ❌ babel.config.js 不存在
)

echo.
echo 3. 检查依赖安装...
if exist "node_modules" (
    echo ✅ node_modules 目录存在
) else (
    echo ❌ node_modules 目录不存在，请运行 npm install
)

if exist "package.json" (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 不存在
)

echo.
echo 4. 检查页面文件...
if exist "pages\simple\simple.vue" (
    echo ✅ 简单测试页面存在
) else (
    echo ❌ 简单测试页面不存在
)

if exist "pages\home\home.vue" (
    echo ✅ 主页存在
) else (
    echo ❌ 主页不存在
)

echo.
echo 5. 建议的解决步骤:
echo.
echo 如果页面空白，请尝试以下步骤:
echo 1. 确保所有文件都存在（上面显示✅）
echo 2. 删除 node_modules 文件夹
echo 3. 运行: npm install
echo 4. 运行: npm run dev:h5
echo 5. 打开浏览器访问: http://localhost:8080
echo.
echo 如果仍然有问题，请检查浏览器控制台的错误信息
echo.

pause
