<template>
  <view class="home-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <text class="navbar-title">健康概览</text>
        <view class="navbar-actions">
          <text @click="goToProfile" class="action-icon">👤</text>
        </view>
      </view>
    </view>
    
    <scroll-view scroll-y class="scroll-content">
      <!-- 用户欢迎区域 -->
      <view class="welcome-section">
        <view class="user-info">
          <image :src="userAvatar" class="avatar" mode="aspectFill" />
          <view class="user-text">
            <text class="greeting">{{ greeting }}</text>
            <text class="username">{{ username }}</text>
          </view>
        </view>
        <view class="health-status">
          <text class="status-text">今日健康状态</text>
          <text class="status-value">良好 ✅</text>
        </view>
      </view>
      
      <!-- 快捷操作 -->
      <view class="quick-actions">
        <text class="section-title">快捷操作</text>
        <view class="action-grid">
          <view @click="goToCamera" class="action-item">
            <view class="action-icon">📸</view>
            <text class="action-text">拍照识别</text>
          </view>
          <view @click="goToReports" class="action-item">
            <view class="action-icon">📋</view>
            <text class="action-text">查看报告</text>
          </view>
          <view @click="goToAnalysis" class="action-item">
            <view class="action-icon">📊</view>
            <text class="action-text">数据分析</text>
          </view>
          <view @click="addReport" class="action-item">
            <view class="action-icon">➕</view>
            <text class="action-text">添加报告</text>
          </view>
        </view>
      </view>
      
      <!-- 最近报告 -->
      <view class="recent-reports">
        <view class="section-header">
          <text class="section-title">最近报告</text>
          <text @click="goToReports" class="more-link">查看全部 →</text>
        </view>
        
        <view v-if="recentReports.length === 0" class="empty-state">
          <text class="empty-icon">📄</text>
          <text class="empty-text">暂无健康报告</text>
          <text @click="goToCamera" class="empty-action">立即添加 →</text>
        </view>
        
        <view v-else class="report-list">
          <view 
            v-for="report in recentReports" 
            :key="report.id"
            @click="viewReport(report)"
            class="report-item"
          >
            <view class="report-icon">🏥</view>
            <view class="report-info">
              <text class="report-title">{{ report.title }}</text>
              <text class="report-date">{{ formatDate(report.date) }}</text>
            </view>
            <text class="report-arrow">→</text>
          </view>
        </view>
      </view>
      
      <!-- 健康提醒 -->
      <view class="health-tips">
        <text class="section-title">健康提醒</text>
        <view class="tip-card">
          <text class="tip-icon">💡</text>
          <view class="tip-content">
            <text class="tip-title">定期体检</text>
            <text class="tip-desc">建议每年进行一次全面体检</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'

export default {
  name: 'HomePage',
  setup() {
    const userStore = useUserStore()
    const appStore = useAppStore()
    
    const recentReports = ref([])
    
    // 计算属性
    const statusBarHeight = computed(() => appStore.statusBarHeight)
    const userAvatar = computed(() => userStore.avatar)
    const username = computed(() => userStore.nickname)
    
    const greeting = computed(() => {
      const hour = new Date().getHours()
      if (hour < 6) return '夜深了'
      if (hour < 12) return '早上好'
      if (hour < 18) return '下午好'
      return '晚上好'
    })
    
    // 页面加载
    onMounted(() => {
      console.log('主页加载完成')
      loadRecentReports()
    })
    
    // 加载最近报告
    const loadRecentReports = () => {
      // 模拟数据，后续从数据库加载
      recentReports.value = []
    }
    
    // 格式化日期
    const formatDate = (dateStr) => {
      const date = new Date(dateStr)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      return date.toLocaleDateString()
    }
    
    // 导航方法
    const goToCamera = () => {
      uni.navigateTo({
        url: '/pages/camera/camera'
      })
    }
    
    const goToReports = () => {
      uni.switchTab({
        url: '/pages/reports/list'
      })
    }
    
    const goToAnalysis = () => {
      uni.switchTab({
        url: '/pages/analysis/analysis'
      })
    }
    
    const goToProfile = () => {
      uni.switchTab({
        url: '/pages/profile/profile'
      })
    }
    
    const addReport = () => {
      uni.navigateTo({
        url: '/pages/reports/add'
      })
    }
    
    const viewReport = (report) => {
      uni.navigateTo({
        url: `/pages/reports/detail?id=${report.id}`
      })
    }
    
    return {
      recentReports,
      statusBarHeight,
      userAvatar,
      username,
      greeting,
      formatDate,
      goToCamera,
      goToReports,
      goToAnalysis,
      goToProfile,
      addReport,
      viewReport
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.custom-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .navbar-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    
    .navbar-title {
      font-size: 36rpx;
      font-weight: 600;
    }
    
    .action-icon {
      font-size: 40rpx;
      padding: 10rpx;
    }
  }
}

.scroll-content {
  height: calc(100vh - 88rpx);
  padding: 0 32rpx;
}

.welcome-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 30rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  
  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      margin-right: 30rpx;
      background-color: #f0f0f0;
    }
    
    .user-text {
      flex: 1;
      
      .greeting {
        display: block;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 8rpx;
      }
      
      .username {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
      }
    }
  }
  
  .health-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-top: 1rpx solid #f0f0f0;
    
    .status-text {
      font-size: 28rpx;
      color: #666;
    }
    
    .status-value {
      font-size: 28rpx;
      font-weight: 600;
      color: #34C759;
    }
  }
}

.quick-actions {
  margin-bottom: 40rpx;
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .action-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20rpx;
    
    .action-item {
      background: white;
      border-radius: 16rpx;
      padding: 40rpx 20rpx;
      text-align: center;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
      
      .action-icon {
        font-size: 60rpx;
        margin-bottom: 16rpx;
      }
      
      .action-text {
        display: block;
        font-size: 26rpx;
        color: #333;
      }
      
      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }
    }
  }
}

.recent-reports {
  margin-bottom: 40rpx;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .more-link {
      font-size: 26rpx;
      color: #007AFF;
    }
  }
  
  .empty-state {
    background: white;
    border-radius: 16rpx;
    padding: 60rpx 40rpx;
    text-align: center;
    
    .empty-icon {
      font-size: 80rpx;
      margin-bottom: 20rpx;
      opacity: 0.5;
    }
    
    .empty-text {
      display: block;
      font-size: 28rpx;
      color: #999;
      margin-bottom: 20rpx;
    }
    
    .empty-action {
      font-size: 26rpx;
      color: #007AFF;
    }
  }
  
  .report-list {
    .report-item {
      background: white;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 16rpx;
      display: flex;
      align-items: center;
      
      .report-icon {
        font-size: 40rpx;
        margin-right: 20rpx;
      }
      
      .report-info {
        flex: 1;
        
        .report-title {
          display: block;
          font-size: 30rpx;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .report-date {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .report-arrow {
        font-size: 28rpx;
        color: #ccc;
      }
    }
  }
}

.health-tips {
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .tip-card {
    background: linear-gradient(135deg, #FF9500 0%, #FF6B35 100%);
    border-radius: 16rpx;
    padding: 30rpx;
    display: flex;
    align-items: center;
    color: white;
    
    .tip-icon {
      font-size: 40rpx;
      margin-right: 20rpx;
    }
    
    .tip-content {
      flex: 1;
      
      .tip-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        margin-bottom: 8rpx;
      }
      
      .tip-desc {
        font-size: 24rpx;
        opacity: 0.9;
      }
    }
  }
}
</style>
