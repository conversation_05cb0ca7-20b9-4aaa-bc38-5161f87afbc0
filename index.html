<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#007AFF">
    <title>健康报告助手</title>
    <link rel="icon" href="/static/logo.png">
    <style>
        /* 页面加载样式 */
        #app-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
        }
        
        .loading-title {
            color: white;
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 30px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 隐藏加载页面 */
        .app-loaded #app-loading {
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 加载页面 -->
        <div id="app-loading">
            <div class="loading-logo">🏥</div>
            <div class="loading-title">健康报告助手</div>
            <div class="loading-spinner"></div>
        </div>
    </div>
    
    <script>
        // 移除加载页面
        window.addEventListener('load', function() {
            setTimeout(function() {
                document.body.classList.add('app-loaded');
                setTimeout(function() {
                    const loading = document.getElementById('app-loading');
                    if (loading) {
                        loading.remove();
                    }
                }, 300);
            }, 1000);
        });
    </script>
</body>
</html>
