<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#007AFF">
    <title>健康报告助手</title>
    <link rel="icon" href="/static/logo.png">
    <style>
        /* 页面加载样式 */
        #app-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
        }
        
        .loading-title {
            color: white;
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 30px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 隐藏加载页面 */
        .app-loaded #app-loading {
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .app-loaded #main-content {
            display: block !important;
        }

        /* Vue应用样式 */
        .container {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: white;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-bottom: 2rem;
            color: white;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .count {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: white;
        }

        .btn {
            background: white;
            color: #667eea;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 1rem;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .btn.secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn.secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .status {
            font-size: 1rem;
            opacity: 0.8;
            margin-top: 2rem;
            color: white;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 加载页面 -->
        <div id="app-loading">
            <div class="loading-logo">🏥</div>
            <div class="loading-title">健康报告助手</div>
            <div class="loading-spinner"></div>
        </div>

        <!-- Vue应用内容 -->
        <div class="container" style="display: none;" id="main-content">
            <h1 class="title">🏥 健康报告助手</h1>
            <p class="subtitle">Vue3 测试页面</p>

            <div class="test-section">
                <p class="count">计数: {{ count }}</p>
                <button @click="increment" class="btn">点击 +1</button>
            </div>

            <div class="nav-section">
                <button @click="showMessage" class="btn secondary">显示消息</button>
                <button @click="testStorage" class="btn secondary">测试存储</button>
            </div>

            <p class="status">{{ status }}</p>
        </div>
    </div>
    
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
        const { createApp, ref, onMounted } = Vue;

        createApp({
            setup() {
                const count = ref(0);
                const status = ref('Vue3 已加载');

                onMounted(() => {
                    console.log('App mounted');
                    status.value = 'Vue3 页面加载成功 ✅';

                    // 移除加载页面并显示主要内容
                    setTimeout(() => {
                        document.body.classList.add('app-loaded');
                        document.getElementById('main-content').style.display = 'block';
                        setTimeout(() => {
                            const loading = document.getElementById('app-loading');
                            if (loading) {
                                loading.remove();
                            }
                        }, 300);
                    }, 1000);
                });

                const increment = () => {
                    count.value++;
                    console.log('Count:', count.value);
                    status.value = `计数器工作正常，当前值: ${count.value}`;
                };

                const showMessage = () => {
                    alert('Hello Vue3!');
                    status.value = '消息显示正常 ✅';
                };

                const testStorage = () => {
                    try {
                        localStorage.setItem('test', 'Vue3 Storage Test');
                        const value = localStorage.getItem('test');
                        status.value = `存储测试成功: ${value}`;
                        alert('存储测试成功');
                    } catch (error) {
                        status.value = '存储测试失败: ' + error.message;
                        console.error('Storage test failed:', error);
                    }
                };

                return {
                    count,
                    status,
                    increment,
                    showMessage,
                    testStorage
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
