<template>
  <view class="camera-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-inset-top">
      <view class="navbar-content">
        <text class="iconfont icon-arrow-left navbar-back" @click="goBack"></text>
        <text class="navbar-title">拍照识别</text>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 相机预览区域 -->
    <view class="camera-container">
      <camera 
        device-position="back"
        flash="off"
        @error="onCameraError"
        class="camera-preview"
      >
        <!-- 拍照指引 -->
        <cover-view class="camera-guide">
          <cover-view class="guide-frame">
            <cover-view class="frame-corner tl"></cover-view>
            <cover-view class="frame-corner tr"></cover-view>
            <cover-view class="frame-corner bl"></cover-view>
            <cover-view class="frame-corner br"></cover-view>
          </cover-view>
          <cover-text class="guide-text">请将报告放在框内</cover-text>
        </cover-view>

        <!-- 拍照按钮 -->
        <cover-view class="camera-controls">
          <cover-view class="control-item" @tap="toggleFlash">
            <cover-text :class="['iconfont', flashMode === 'on' ? 'icon-flash' : 'icon-flash-off']"></cover-text>
          </cover-view>
          
          <cover-view class="capture-btn" @tap="takePhoto">
            <cover-view class="capture-inner"></cover-view>
          </cover-view>
          
          <cover-view class="control-item" @tap="chooseFromAlbum">
            <cover-text class="iconfont icon-image"></cover-text>
          </cover-view>
        </cover-view>
      </camera>
    </view>

    <!-- 拍照提示 -->
    <view class="photo-tips">
      <view class="tip-item">
        <text class="iconfont icon-lightbulb tip-icon"></text>
        <text class="tip-text">确保光线充足，避免阴影遮挡</text>
      </view>
      <view class="tip-item">
        <text class="iconfont icon-target tip-icon"></text>
        <text class="tip-text">将报告完整放入取景框内</text>
      </view>
      <view class="tip-item">
        <text class="iconfont icon-focus tip-icon"></text>
        <text class="tip-text">保持手机稳定，避免模糊</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CameraPage',
  data() {
    return {
      flashMode: 'off',
      cameraContext: null
    }
  },
  
  onLoad() {
    this.initCamera()
  },
  
  onUnload() {
    // 清理相机资源
    if (this.cameraContext) {
      this.cameraContext.stopRecord()
    }
  },
  
  methods: {
    // 初始化相机
    initCamera() {
      this.cameraContext = uni.createCameraContext()
    },
    
    // 拍照
    takePhoto() {
      if (!this.cameraContext) {
        uni.showToast({
          title: '相机初始化失败',
          icon: 'none'
        })
        return
      }
      
      uni.showLoading({ title: '拍照中...' })
      
      this.cameraContext.takePhoto({
        quality: 'high',
        success: (res) => {
          uni.hideLoading()
          this.handlePhotoTaken(res.tempImagePath)
        },
        fail: (error) => {
          uni.hideLoading()
          console.error('拍照失败:', error)
          uni.showToast({
            title: '拍照失败，请重试',
            icon: 'none'
          })
        }
      })
    },
    
    // 处理拍照结果
    handlePhotoTaken(imagePath) {
      // 跳转到报告添加页面，并传递图片路径
      uni.navigateTo({
        url: `/pages/reports/add?imagePath=${encodeURIComponent(imagePath)}`
      })
    },
    
    // 从相册选择
    chooseFromAlbum() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album'],
        success: (res) => {
          this.handlePhotoTaken(res.tempFilePaths[0])
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 切换闪光灯
    toggleFlash() {
      this.flashMode = this.flashMode === 'on' ? 'off' : 'on'
      
      // 注意：uni-app的camera组件闪光灯控制有限
      // 实际项目中可能需要使用原生插件
      uni.showToast({
        title: `闪光灯${this.flashMode === 'on' ? '开启' : '关闭'}`,
        icon: 'none',
        duration: 1000
      })
    },
    
    // 相机错误处理
    onCameraError(error) {
      console.error('相机错误:', error)
      uni.showModal({
        title: '相机错误',
        content: '无法访问相机，请检查权限设置',
        confirmText: '去设置',
        success: (res) => {
          if (res.confirm) {
            uni.openSetting()
          }
        }
      })
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.camera-page {
  height: 100vh;
  background-color: #000000;
  position: relative;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  
  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx;
    height: 88rpx;
    
    .navbar-back {
      font-size: 40rpx;
      color: #ffffff;
      padding: 16rpx;
    }
    
    .navbar-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #ffffff;
    }
    
    .navbar-right {
      width: 72rpx;
    }
  }
}

.camera-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.camera-preview {
  width: 100%;
  height: 100%;
}

.camera-guide {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  height: 400rpx;
  
  .guide-frame {
    position: relative;
    width: 100%;
    height: 100%;
    border: 4rpx solid rgba(255, 255, 255, 0.8);
    border-radius: 16rpx;
    
    .frame-corner {
      position: absolute;
      width: 40rpx;
      height: 40rpx;
      border: 6rpx solid #ffffff;
      
      &.tl {
        top: -6rpx;
        left: -6rpx;
        border-right: none;
        border-bottom: none;
        border-top-left-radius: 16rpx;
      }
      
      &.tr {
        top: -6rpx;
        right: -6rpx;
        border-left: none;
        border-bottom: none;
        border-top-right-radius: 16rpx;
      }
      
      &.bl {
        bottom: -6rpx;
        left: -6rpx;
        border-right: none;
        border-top: none;
        border-bottom-left-radius: 16rpx;
      }
      
      &.br {
        bottom: -6rpx;
        right: -6rpx;
        border-left: none;
        border-top: none;
        border-bottom-right-radius: 16rpx;
      }
    }
  }
  
  .guide-text {
    position: absolute;
    bottom: -80rpx;
    left: 50%;
    transform: translateX(-50%);
    color: #ffffff;
    font-size: 28rpx;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 16rpx 32rpx;
    border-radius: 24rpx;
  }
}

.camera-controls {
  position: absolute;
  bottom: 120rpx;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 80rpx;
  
  .control-item {
    width: 96rpx;
    height: 96rpx;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
    
    .iconfont {
      font-size: 48rpx;
      color: #ffffff;
    }
  }
  
  .capture-btn {
    width: 120rpx;
    height: 120rpx;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    border: 6rpx solid #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .capture-inner {
      width: 80rpx;
      height: 80rpx;
      background-color: #ffffff;
      border-radius: 50%;
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}

.photo-tips {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  
  .tip-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .tip-icon {
      font-size: 32rpx;
      color: #ffffff;
      margin-right: 16rpx;
      opacity: 0.8;
    }
    
    .tip-text {
      font-size: 24rpx;
      color: #ffffff;
      opacity: 0.9;
      line-height: 1.4;
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .camera-page {
    background-color: #000000;
  }
}
</style>
