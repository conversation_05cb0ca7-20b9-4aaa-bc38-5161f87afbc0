<template>
  <view :class="cardClass" @click="handleClick">
    <!-- 卡片头部 -->
    <view v-if="$slots.header || title" class="card-header">
      <slot name="header">
        <text v-if="title" class="card-title">{{ title }}</text>
        <text v-if="subtitle" class="card-subtitle">{{ subtitle }}</text>
      </slot>
    </view>
    
    <!-- 卡片内容 -->
    <view class="card-content">
      <slot></slot>
    </view>
    
    <!-- 卡片底部 -->
    <view v-if="$slots.footer" class="card-footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomCard',
  props: {
    // 卡片标题
    title: {
      type: String,
      default: ''
    },
    
    // 卡片副标题
    subtitle: {
      type: String,
      default: ''
    },
    
    // 是否有阴影
    shadow: {
      type: Boolean,
      default: true
    },
    
    // 阴影大小
    shadowSize: {
      type: String,
      default: 'medium', // small, medium, large
      validator: (value) => {
        return ['small', 'medium', 'large'].includes(value)
      }
    },
    
    // 是否有边框
    border: {
      type: Boolean,
      default: false
    },
    
    // 圆角大小
    radius: {
      type: String,
      default: 'medium', // small, medium, large
      validator: (value) => {
        return ['small', 'medium', 'large'].includes(value)
      }
    },
    
    // 内边距
    padding: {
      type: String,
      default: 'medium', // small, medium, large, none
      validator: (value) => {
        return ['none', 'small', 'medium', 'large'].includes(value)
      }
    },
    
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    
    // 背景色
    background: {
      type: String,
      default: 'white' // white, transparent, primary, success, warning, error
    },
    
    // 是否悬浮
    floating: {
      type: Boolean,
      default: false
    }
  },
  
  computed: {
    cardClass() {
      const classes = ['custom-card']
      
      // 阴影样式
      if (this.shadow) {
        classes.push(`card-shadow-${this.shadowSize}`)
      }
      
      // 边框样式
      if (this.border) {
        classes.push('card-border')
      }
      
      // 圆角样式
      classes.push(`card-radius-${this.radius}`)
      
      // 内边距样式
      classes.push(`card-padding-${this.padding}`)
      
      // 背景样式
      classes.push(`card-bg-${this.background}`)
      
      // 交互样式
      if (this.clickable) {
        classes.push('card-clickable')
      }
      
      // 悬浮样式
      if (this.floating) {
        classes.push('card-floating')
      }
      
      return classes.join(' ')
    }
  },
  
  methods: {
    handleClick(event) {
      if (this.clickable) {
        this.$emit('click', event)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-card {
  position: relative;
  background-color: var(--card-background);
  transition: all 0.3s ease;
  overflow: hidden;
  
  // 阴影样式
  &.card-shadow-small {
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  }
  
  &.card-shadow-medium {
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
  
  &.card-shadow-large {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  }
  
  // 边框样式
  &.card-border {
    border: 2rpx solid var(--border-color);
  }
  
  // 圆角样式
  &.card-radius-small {
    border-radius: 8rpx;
  }
  
  &.card-radius-medium {
    border-radius: 16rpx;
  }
  
  &.card-radius-large {
    border-radius: 24rpx;
  }
  
  // 内边距样式
  &.card-padding-none {
    padding: 0;
  }
  
  &.card-padding-small {
    padding: 16rpx;
  }
  
  &.card-padding-medium {
    padding: 32rpx;
  }
  
  &.card-padding-large {
    padding: 48rpx;
  }
  
  // 背景样式
  &.card-bg-white {
    background-color: #ffffff;
  }
  
  &.card-bg-transparent {
    background-color: transparent;
  }
  
  &.card-bg-primary {
    background-color: var(--primary-color);
    color: #ffffff;
  }
  
  &.card-bg-success {
    background-color: var(--success-color);
    color: #ffffff;
  }
  
  &.card-bg-warning {
    background-color: var(--warning-color);
    color: #ffffff;
  }
  
  &.card-bg-error {
    background-color: var(--error-color);
    color: #ffffff;
  }
  
  // 可点击样式
  &.card-clickable {
    cursor: pointer;
    
    &:active {
      transform: scale(0.98);
    }
    
    &:hover {
      box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
    }
  }
  
  // 悬浮样式
  &.card-floating {
    position: relative;
    z-index: 10;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
      pointer-events: none;
    }
  }
}

.card-header {
  margin-bottom: 24rpx;
  
  .card-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8rpx;
  }
  
  .card-subtitle {
    display: block;
    font-size: 28rpx;
    color: var(--text-secondary);
  }
}

.card-content {
  flex: 1;
}

.card-footer {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid var(--border-color);
}

// 特殊卡片样式
.custom-card.card-bg-primary,
.custom-card.card-bg-success,
.custom-card.card-bg-warning,
.custom-card.card-bg-error {
  .card-title {
    color: #ffffff;
  }
  
  .card-subtitle {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .card-footer {
    border-top-color: rgba(255, 255, 255, 0.2);
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .custom-card {
    &.card-padding-medium {
      padding: 24rpx;
    }
    
    &.card-padding-large {
      padding: 32rpx;
    }
  }
  
  .card-header {
    margin-bottom: 16rpx;
    
    .card-title {
      font-size: 28rpx;
    }
    
    .card-subtitle {
      font-size: 24rpx;
    }
  }
}
</style>
