import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    isLoggedIn: false,
    token: '',
    biometricEnabled: false,
    settings: {
      autoSync: true,
      notificationEnabled: true,
      theme: 'auto', // light/dark/auto
      biometricEnabled: false
    }
  }),

  getters: {
    isAuthenticated: (state) => !!state.token && state.isLoggedIn,
    userAge: (state) => {
      if (!state.userInfo?.birthday) return null
      const today = new Date()
      const birthDate = new Date(state.userInfo.birthday)
      let age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }
      return age
    },
    userBMI: (state) => {
      if (!state.userInfo?.height || !state.userInfo?.weight) return null
      const height = state.userInfo.height / 100 // 转换为米
      const bmi = state.userInfo.weight / (height * height)
      return Math.round(bmi * 10) / 10
    }
  },

  actions: {
    // 登录
    async login(credentials) {
      try {
        uni.showLoading({ title: '登录中...' })
        
        // 模拟API调用
        const response = await this.mockLogin(credentials)
        
        if (response.success) {
          this.token = response.token
          this.userInfo = response.userInfo
          this.isLoggedIn = true
          
          // 保存到本地存储
          uni.setStorageSync('token', response.token)
          uni.setStorageSync('userInfo', response.userInfo)
          
          uni.hideLoading()
          return response
        } else {
          throw new Error(response.message || '登录失败')
        }
      } catch (error) {
        uni.hideLoading()
        throw error
      }
    },

    // 短信验证码登录
    async loginWithSms(credentials) {
      try {
        uni.showLoading({ title: '登录中...' })
        
        // 模拟API调用
        const response = await this.mockSmsLogin(credentials)
        
        if (response.success) {
          this.token = response.token
          this.userInfo = response.userInfo
          this.isLoggedIn = true
          
          // 保存到本地存储
          uni.setStorageSync('token', response.token)
          uni.setStorageSync('userInfo', response.userInfo)
          
          uni.hideLoading()
          return response
        } else {
          throw new Error(response.message || '登录失败')
        }
      } catch (error) {
        uni.hideLoading()
        throw error
      }
    },

    // 注册
    async register(userInfo) {
      try {
        uni.showLoading({ title: '注册中...' })
        
        // 模拟API调用
        const response = await this.mockRegister(userInfo)
        
        if (response.success) {
          uni.hideLoading()
          uni.showToast({
            title: '注册成功',
            icon: 'success'
          })
          return response
        } else {
          throw new Error(response.message || '注册失败')
        }
      } catch (error) {
        uni.hideLoading()
        throw error
      }
    },

    // 生物识别登录
    async biometricLogin() {
      try {
        const token = uni.getStorageSync('token')
        if (!token) {
          throw new Error('未找到登录凭证')
        }

        const userInfo = uni.getStorageSync('userInfo')
        
        // 验证token有效性
        const isValid = await this.validateToken(token)
        if (isValid) {
          this.token = token
          this.userInfo = userInfo
          this.isLoggedIn = true
          return { success: true }
        } else {
          throw new Error('登录凭证已过期')
        }
      } catch (error) {
        // 清除无效凭证
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        throw error
      }
    },

    // 退出登录
    async logout() {
      try {
        // 清除状态
        this.token = ''
        this.userInfo = null
        this.isLoggedIn = false
        
        // 清除本地存储
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        
        // 清除敏感缓存
        uni.clearStorageSync()
        
        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        })
      } catch (error) {
        console.error('退出登录失败:', error)
      }
    },

    // 重置密码
    async resetPassword(phone) {
      try {
        uni.showLoading({ title: '发送中...' })
        
        // 模拟API调用
        const response = await this.mockResetPassword(phone)
        
        uni.hideLoading()
        
        if (response.success) {
          uni.showToast({
            title: '重置链接已发送',
            icon: 'success'
          })
          return response
        } else {
          throw new Error(response.message || '发送失败')
        }
      } catch (error) {
        uni.hideLoading()
        throw error
      }
    },

    // 更新用户信息
    async updateUserInfo(newUserInfo) {
      try {
        uni.showLoading({ title: '更新中...' })
        
        // 模拟API调用
        const response = await this.mockUpdateUserInfo(newUserInfo)
        
        if (response.success) {
          this.userInfo = { ...this.userInfo, ...newUserInfo }
          uni.setStorageSync('userInfo', this.userInfo)
          
          uni.hideLoading()
          uni.showToast({
            title: '更新成功',
            icon: 'success'
          })
          return response
        } else {
          throw new Error(response.message || '更新失败')
        }
      } catch (error) {
        uni.hideLoading()
        throw error
      }
    },

    // 启用生物识别
    async enableBiometric() {
      try {
        // #ifdef APP-PLUS
        const result = await uni.startFacialRecognitionVerify({
          message: '请验证身份以启用生物识别'
        })
        
        this.biometricEnabled = true
        this.settings.biometricEnabled = true
        
        // 保存设置
        uni.setStorageSync('biometricEnabled', true)
        
        uni.showToast({
          title: '生物识别已启用',
          icon: 'success'
        })
        
        return { success: true }
        // #endif
        
        // #ifndef APP-PLUS
        throw new Error('当前平台不支持生物识别')
        // #endif
      } catch (error) {
        throw error
      }
    },

    // 模拟API方法
    async mockLogin(credentials) {
      await this.delay(1000)
      
      // 简单验证
      if (credentials.phone === '13800138000' && credentials.password === '123456') {
        return {
          success: true,
          token: 'mock_token_' + Date.now(),
          userInfo: {
            id: '1',
            phone: credentials.phone,
            nickname: '健康用户',
            avatar: '',
            gender: 'male',
            birthday: '1990-01-01',
            height: 175,
            weight: 70,
            createdAt: new Date().toISOString()
          }
        }
      } else {
        return {
          success: false,
          message: '手机号或密码错误'
        }
      }
    },

    async mockSmsLogin(credentials) {
      await this.delay(1000)
      
      // 简单验证
      if (credentials.phone.length === 11 && credentials.smsCode === '123456') {
        return {
          success: true,
          token: 'mock_token_' + Date.now(),
          userInfo: {
            id: '1',
            phone: credentials.phone,
            nickname: '健康用户',
            avatar: '',
            createdAt: new Date().toISOString()
          }
        }
      } else {
        return {
          success: false,
          message: '验证码错误'
        }
      }
    },

    async mockRegister(userInfo) {
      await this.delay(1000)
      return {
        success: true,
        message: '注册成功'
      }
    },

    async mockResetPassword(phone) {
      await this.delay(1000)
      return {
        success: true,
        message: '重置链接已发送'
      }
    },

    async mockUpdateUserInfo(newUserInfo) {
      await this.delay(1000)
      return {
        success: true,
        userInfo: newUserInfo
      }
    },

    async validateToken(token) {
      await this.delay(500)
      // 简单验证token格式
      return token && token.startsWith('mock_token_')
    },

    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
})
