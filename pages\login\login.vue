<template>
  <view class="login-page">
    <view class="container">
      <!-- Logo和标题 -->
      <view class="logo-section">
        <image src="/static/images/logo.png" class="logo" mode="aspectFit" />
        <text class="app-name">健康报告助手</text>
        <text class="app-desc">管理您的健康数据</text>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <!-- 手机号输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <text class="iconfont icon-phone input-icon"></text>
            <input 
              v-model="loginForm.phone"
              type="number"
              placeholder="请输入手机号"
              class="input-field"
              maxlength="11"
            />
          </view>
        </view>

        <!-- 密码输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <text class="iconfont icon-lock input-icon"></text>
            <input 
              v-model="loginForm.password"
              :password="!showPassword"
              placeholder="请输入密码"
              class="input-field"
            />
            <text 
              :class="['iconfont', showPassword ? 'icon-eye' : 'icon-eye-off', 'password-toggle']"
              @click="togglePassword"
            ></text>
          </view>
        </view>

        <!-- 验证码输入（可选） -->
        <view v-if="loginMode === 'sms'" class="input-group">
          <view class="input-wrapper">
            <text class="iconfont icon-message input-icon"></text>
            <input 
              v-model="loginForm.smsCode"
              type="number"
              placeholder="请输入验证码"
              class="input-field"
              maxlength="6"
            />
            <button 
              :disabled="smsCountdown > 0"
              @click="sendSmsCode"
              class="sms-btn"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
            </button>
          </view>
        </view>

        <!-- 登录方式切换 -->
        <view class="login-mode-switch">
          <text 
            :class="['mode-btn', { active: loginMode === 'password' }]"
            @click="switchLoginMode('password')"
          >
            密码登录
          </text>
          <text 
            :class="['mode-btn', { active: loginMode === 'sms' }]"
            @click="switchLoginMode('sms')"
          >
            验证码登录
          </text>
        </view>

        <!-- 登录按钮 -->
        <button 
          :disabled="!canLogin"
          @click="handleLogin"
          :class="['login-btn', { disabled: !canLogin }]"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>

        <!-- 生物识别登录 -->
        <view v-if="biometricSupported" class="biometric-section">
          <view class="divider">
            <text class="divider-text">或</text>
          </view>
          <button @click="biometricLogin" class="biometric-btn">
            <text class="iconfont icon-fingerprint"></text>
            <text class="biometric-text">指纹/面容登录</text>
          </button>
        </view>

        <!-- 底部链接 -->
        <view class="bottom-links">
          <text class="link-text" @click="forgotPassword">忘记密码？</text>
          <text class="link-text" @click="goRegister">注册账号</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useUserStore } from '@/stores/user'

export default {
  name: 'LoginPage',
  data() {
    return {
      loginForm: {
        phone: '',
        password: '',
        smsCode: ''
      },
      loginMode: 'password', // password | sms
      showPassword: false,
      loading: false,
      smsCountdown: 0,
      biometricSupported: false
    }
  },
  computed: {
    canLogin() {
      if (this.loginMode === 'password') {
        return this.loginForm.phone.length === 11 && this.loginForm.password.length >= 6
      } else {
        return this.loginForm.phone.length === 11 && this.loginForm.smsCode.length === 6
      }
    }
  },
  onLoad() {
    this.checkBiometricSupport()
  },
  methods: {
    async checkBiometricSupport() {
      // #ifdef APP-PLUS
      try {
        const result = await uni.checkIsSupportFaceID()
        this.biometricSupported = true
      } catch (error) {
        this.biometricSupported = false
      }
      // #endif
    },
    
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    switchLoginMode(mode) {
      this.loginMode = mode
      // 清空表单
      this.loginForm.password = ''
      this.loginForm.smsCode = ''
    },
    
    async sendSmsCode() {
      if (!this.loginForm.phone || this.loginForm.phone.length !== 11) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
      
      // 开始倒计时
      this.smsCountdown = 60
      const timer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(timer)
        }
      }, 1000)
      
      try {
        // 发送验证码
        console.log('发送验证码到:', this.loginForm.phone)
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: '发送失败，请重试',
          icon: 'none'
        })
        this.smsCountdown = 0
        clearInterval(timer)
      }
    },
    
    async handleLogin() {
      if (!this.canLogin || this.loading) return
      
      this.loading = true
      
      try {
        const userStore = useUserStore()
        
        if (this.loginMode === 'password') {
          await userStore.login({
            phone: this.loginForm.phone,
            password: this.loginForm.password
          })
        } else {
          await userStore.loginWithSms({
            phone: this.loginForm.phone,
            smsCode: this.loginForm.smsCode
          })
        }
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 跳转到首页
        uni.switchTab({
          url: '/pages/home/<USER>'
        })
        
      } catch (error) {
        uni.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    async biometricLogin() {
      try {
        // #ifdef APP-PLUS
        await uni.startFacialRecognitionVerify({
          message: '请验证身份'
        })
        
        // 生物识别成功，使用本地存储的凭证登录
        const userStore = useUserStore()
        await userStore.biometricLogin()
        
        uni.switchTab({
          url: '/pages/home/<USER>'
        })
        // #endif
      } catch (error) {
        uni.showToast({
          title: '验证失败',
          icon: 'none'
        })
      }
    },
    
    forgotPassword() {
      uni.navigateTo({
        url: '/pages/login/forgot'
      })
    },
    
    goRegister() {
      uni.navigateTo({
        url: '/pages/login/register'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.container {
  padding: 48rpx 32rpx;
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
}

.logo-section {
  text-align: center;
  margin-bottom: 96rpx;
  
  .logo {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 32rpx;
  }
  
  .app-name {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 16rpx;
  }
  
  .app-desc {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.login-form {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 32rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--background-color);
  border-radius: 16rpx;
  padding: 24rpx;
  
  .input-icon {
    font-size: 36rpx;
    color: var(--text-secondary);
    margin-right: 16rpx;
  }
  
  .input-field {
    flex: 1;
    font-size: 32rpx;
    color: var(--text-color);
    background: transparent;
    border: none;
    outline: none;
  }
  
  .password-toggle {
    font-size: 36rpx;
    color: var(--text-secondary);
    padding: 8rpx;
  }
  
  .sms-btn {
    background-color: var(--primary-color);
    color: #ffffff;
    border: none;
    border-radius: 12rpx;
    padding: 16rpx 24rpx;
    font-size: 24rpx;
    
    &:disabled {
      background-color: var(--text-secondary);
    }
  }
}

.login-mode-switch {
  display: flex;
  background-color: var(--background-color);
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 48rpx;
  
  .mode-btn {
    flex: 1;
    text-align: center;
    padding: 16rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    
    &.active {
      background-color: #ffffff;
      color: var(--primary-color);
      font-weight: 500;
    }
  }
}

.login-btn {
  width: 100%;
  background-color: var(--primary-color);
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  
  &.disabled {
    background-color: var(--text-secondary);
    opacity: 0.6;
  }
  
  &:active:not(.disabled) {
    opacity: 0.8;
  }
}

.biometric-section {
  .divider {
    text-align: center;
    margin: 32rpx 0;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1rpx;
      background-color: var(--border-color);
    }
    
    .divider-text {
      background-color: #ffffff;
      color: var(--text-secondary);
      padding: 0 16rpx;
      font-size: 24rpx;
    }
  }
  
  .biometric-btn {
    width: 100%;
    background-color: transparent;
    border: 2rpx solid var(--border-color);
    border-radius: 16rpx;
    padding: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .iconfont {
      font-size: 36rpx;
      color: var(--primary-color);
      margin-right: 16rpx;
    }
    
    .biometric-text {
      font-size: 32rpx;
      color: var(--text-color);
    }
  }
}

.bottom-links {
  display: flex;
  justify-content: space-between;
  margin-top: 32rpx;
  
  .link-text {
    font-size: 28rpx;
    color: var(--primary-color);
  }
}
</style>
