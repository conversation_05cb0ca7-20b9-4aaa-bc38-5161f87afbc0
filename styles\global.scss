/* 全局样式变量 */
:root {
  --primary-color: #007AFF;
  --success-color: #34C759;
  --warning-color: #FF9500;
  --error-color: #FF3B30;
  --text-color: #000000;
  --text-secondary: #8E8E93;
  --background-color: #F2F2F7;
  --card-background: #FFFFFF;
  --border-color: #C6C6C8;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* 通用样式类 */
.container {
  padding: 32rpx;
  background-color: var(--background-color);
  min-height: 100vh;
}

.card {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx var(--shadow-color);
}

.btn-primary {
  background-color: var(--primary-color);
  color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  text-align: center;
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  border-radius: 12rpx;
  padding: 22rpx 46rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
}

.text-primary {
  color: var(--text-color);
  font-size: 32rpx;
  line-height: 1.5;
}

.text-secondary {
  color: var(--text-secondary);
  font-size: 28rpx;
  line-height: 1.4;
}

.text-title {
  color: var(--text-color);
  font-size: 36rpx;
  font-weight: 600;
  line-height: 1.3;
}

.text-subtitle {
  color: var(--text-color);
  font-size: 34rpx;
  font-weight: 500;
  line-height: 1.4;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 间距样式 */
.mt-8 { margin-top: 16rpx; }
.mt-12 { margin-top: 24rpx; }
.mt-16 { margin-top: 32rpx; }
.mt-20 { margin-top: 40rpx; }
.mt-24 { margin-top: 48rpx; }

.mb-8 { margin-bottom: 16rpx; }
.mb-12 { margin-bottom: 24rpx; }
.mb-16 { margin-bottom: 32rpx; }
.mb-20 { margin-bottom: 40rpx; }
.mb-24 { margin-bottom: 48rpx; }

.ml-8 { margin-left: 16rpx; }
.ml-12 { margin-left: 24rpx; }
.ml-16 { margin-left: 32rpx; }

.mr-8 { margin-right: 16rpx; }
.mr-12 { margin-right: 24rpx; }
.mr-16 { margin-right: 32rpx; }

.p-8 { padding: 16rpx; }
.p-12 { padding: 24rpx; }
.p-16 { padding: 32rpx; }
.p-20 { padding: 40rpx; }

/* 圆角样式 */
.rounded-sm { border-radius: 8rpx; }
.rounded { border-radius: 12rpx; }
.rounded-lg { border-radius: 16rpx; }
.rounded-xl { border-radius: 24rpx; }
.rounded-full { border-radius: 50%; }

/* 阴影样式 */
.shadow-sm {
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.shadow {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 状态样式 */
.status-normal {
  color: var(--success-color);
  background-color: rgba(52, 199, 89, 0.1);
}

.status-warning {
  color: var(--warning-color);
  background-color: rgba(255, 149, 0, 0.1);
}

.status-error {
  color: var(--error-color);
  background-color: rgba(255, 59, 48, 0.1);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .container {
    padding: 24rpx;
  }
  
  .card {
    padding: 24rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #FFFFFF;
    --text-secondary: #8E8E93;
    --background-color: #000000;
    --card-background: #1C1C1E;
    --border-color: #38383A;
  }
}
