<template>
  <view class="test-page">
    <view class="container">
      <text class="title">Vue3 测试页面</text>
      
      <view class="test-section">
        <text class="section-title">响应式数据测试</text>
        <text class="count-text">计数: {{ count }}</text>
        <button @click="increment" class="test-btn">点击增加</button>
      </view>
      
      <view class="test-section">
        <text class="section-title">组合式API测试</text>
        <text class="message-text">{{ message }}</text>
        <button @click="changeMessage" class="test-btn">改变消息</button>
      </view>
      
      <view class="test-section">
        <text class="section-title">生命周期测试</text>
        <text class="lifecycle-text">{{ lifecycleInfo }}</text>
      </view>
      
      <view class="test-section">
        <text class="section-title">Pinia状态管理测试</text>
        <text class="store-text">用户状态: {{ userStore.isLoggedIn ? '已登录' : '未登录' }}</text>
        <button @click="toggleLogin" class="test-btn">切换登录状态</button>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'

export default {
  name: 'TestPage',
  setup() {
    // 响应式数据
    const count = ref(0)
    const message = ref('Hello Vue 3!')
    const lifecycleInfo = ref('')
    
    // 状态管理
    const userStore = useUserStore()
    
    // 方法
    const increment = () => {
      count.value++
    }
    
    const changeMessage = () => {
      const messages = [
        'Hello Vue 3!',
        'Composition API 很棒!',
        'uni-app + Vue 3 = 💪',
        '健康报告助手'
      ]
      const currentIndex = messages.indexOf(message.value)
      const nextIndex = (currentIndex + 1) % messages.length
      message.value = messages[nextIndex]
    }
    
    const toggleLogin = () => {
      // 这里只是测试，实际应该调用登录方法
      userStore.isLoggedIn = !userStore.isLoggedIn
    }
    
    // 生命周期
    onMounted(() => {
      lifecycleInfo.value = 'onMounted 已执行 ✅'
      console.log('Vue 3 测试页面已挂载')
    })
    
    return {
      count,
      message,
      lifecycleInfo,
      userStore,
      increment,
      changeMessage,
      toggleLogin
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  background-color: var(--background-color);
  min-height: 100vh;
}

.container {
  padding: 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: var(--text-color);
  text-align: center;
  margin-bottom: 48rpx;
}

.test-section {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 24rpx;
  }
  
  .count-text,
  .message-text,
  .lifecycle-text,
  .store-text {
    display: block;
    font-size: 28rpx;
    color: var(--text-secondary);
    margin-bottom: 24rpx;
    line-height: 1.5;
  }
  
  .test-btn {
    background-color: var(--primary-color);
    color: #ffffff;
    border: none;
    border-radius: 12rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
    
    &:active {
      opacity: 0.8;
    }
  }
}
</style>
