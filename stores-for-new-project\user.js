import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 用户基本信息
    userInfo: null,
    token: '',
    isLoggedIn: false,
    
    // 用户设置
    settings: {
      theme: 'light',
      notifications: true,
      biometric: false,
      autoSync: true
    },
    
    // 登录状态
    loginLoading: false,
    registerLoading: false
  }),
  
  getters: {
    // 获取用户头像
    avatar: (state) => {
      return state.userInfo?.avatar || '/static/images/default-avatar.png'
    },
    
    // 获取用户昵称
    nickname: (state) => {
      return state.userInfo?.nickname || state.userInfo?.phone || '用户'
    },
    
    // 检查是否已完善个人信息
    isProfileComplete: (state) => {
      if (!state.userInfo) return false
      return !!(state.userInfo.nickname && state.userInfo.gender && state.userInfo.birthday)
    }
  },
  
  actions: {
    // 初始化用户状态
    async initUser() {
      try {
        const token = uni.getStorageSync('token')
        const userInfo = uni.getStorageSync('userInfo')
        const settings = uni.getStorageSync('userSettings')
        
        if (token) {
          this.token = token
          this.isLoggedIn = true
        }
        
        if (userInfo) {
          this.userInfo = JSON.parse(userInfo)
        }
        
        if (settings) {
          this.settings = { ...this.settings, ...JSON.parse(settings) }
        }
        
        console.log('用户状态初始化完成')
      } catch (error) {
        console.error('用户状态初始化失败:', error)
      }
    },
    
    // 用户登录
    async login(credentials) {
      this.loginLoading = true
      
      try {
        // 模拟API调用
        const response = await this.mockLogin(credentials)
        
        if (response.success) {
          // 保存用户信息
          this.token = response.token
          this.userInfo = response.userInfo
          this.isLoggedIn = true
          
          // 持久化存储
          uni.setStorageSync('token', response.token)
          uni.setStorageSync('userInfo', JSON.stringify(response.userInfo))
          
          console.log('登录成功')
          return { success: true }
        } else {
          throw new Error(response.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, message: error.message }
      } finally {
        this.loginLoading = false
      }
    },
    
    // 用户注册
    async register(userInfo) {
      this.registerLoading = true
      
      try {
        const response = await this.mockRegister(userInfo)
        
        if (response.success) {
          console.log('注册成功')
          return { success: true }
        } else {
          throw new Error(response.message || '注册失败')
        }
      } catch (error) {
        console.error('注册失败:', error)
        return { success: false, message: error.message }
      } finally {
        this.registerLoading = false
      }
    },
    
    // 用户退出
    async logout() {
      try {
        // 清除状态
        this.token = ''
        this.userInfo = null
        this.isLoggedIn = false
        
        // 清除本地存储
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        
        console.log('退出登录成功')
        
        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/login'
        })
      } catch (error) {
        console.error('退出登录失败:', error)
      }
    },
    
    // 更新用户信息
    async updateProfile(newInfo) {
      try {
        this.userInfo = { ...this.userInfo, ...newInfo }
        uni.setStorageSync('userInfo', JSON.stringify(this.userInfo))
        
        console.log('用户信息更新成功')
        return { success: true }
      } catch (error) {
        console.error('用户信息更新失败:', error)
        return { success: false, message: error.message }
      }
    },
    
    // 更新用户设置
    async updateSettings(newSettings) {
      try {
        this.settings = { ...this.settings, ...newSettings }
        uni.setStorageSync('userSettings', JSON.stringify(this.settings))
        
        console.log('用户设置更新成功')
        return { success: true }
      } catch (error) {
        console.error('用户设置更新失败:', error)
        return { success: false, message: error.message }
      }
    },
    
    // 模拟登录API
    async mockLogin(credentials) {
      await this.delay(1000) // 模拟网络延迟
      
      // 简单的模拟验证
      if (credentials.phone === '13800138000' && credentials.password === '123456') {
        return {
          success: true,
          token: 'mock_token_' + Date.now(),
          userInfo: {
            id: '1',
            phone: credentials.phone,
            nickname: '健康用户',
            avatar: '',
            gender: '',
            birthday: '',
            createdAt: new Date().toISOString()
          }
        }
      } else {
        return {
          success: false,
          message: '手机号或密码错误'
        }
      }
    },
    
    // 模拟注册API
    async mockRegister(userInfo) {
      await this.delay(1000)
      
      // 简单验证
      if (userInfo.phone && userInfo.password) {
        return {
          success: true,
          message: '注册成功'
        }
      } else {
        return {
          success: false,
          message: '请填写完整信息'
        }
      }
    },
    
    // 工具函数：延迟
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
})
