/**
 * 性能监控工具
 */

// 性能指标收集器
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = []
    this.config = {
      enableMemoryMonitoring: true,
      enableNetworkMonitoring: true,
      enablePageLoadMonitoring: true,
      reportInterval: 60000, // 1分钟
      maxMetricsCount: 1000
    }
    
    this.init()
  }

  // 初始化性能监控
  init() {
    this.setupPageLoadMonitoring()
    this.setupMemoryMonitoring()
    this.setupNetworkMonitoring()
    this.startReporting()
  }

  // 页面加载性能监控
  setupPageLoadMonitoring() {
    if (!this.config.enablePageLoadMonitoring) return

    // #ifdef H5
    if (typeof window !== 'undefined' && window.performance) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          this.collectPageLoadMetrics()
        }, 0)
      })
    }
    // #endif

    // uni-app页面性能监控
    if (typeof uni !== 'undefined') {
      const originalNavigateTo = uni.navigateTo
      const originalSwitchTab = uni.switchTab
      
      uni.navigateTo = (options) => {
        const startTime = Date.now()
        const originalSuccess = options.success
        
        options.success = (res) => {
          this.recordMetric('page_navigation', {
            url: options.url,
            duration: Date.now() - startTime,
            type: 'navigateTo'
          })
          
          if (originalSuccess) originalSuccess(res)
        }
        
        return originalNavigateTo(options)
      }
      
      uni.switchTab = (options) => {
        const startTime = Date.now()
        const originalSuccess = options.success
        
        options.success = (res) => {
          this.recordMetric('page_navigation', {
            url: options.url,
            duration: Date.now() - startTime,
            type: 'switchTab'
          })
          
          if (originalSuccess) originalSuccess(res)
        }
        
        return originalSwitchTab(options)
      }
    }
  }

  // 收集页面加载指标
  collectPageLoadMetrics() {
    // #ifdef H5
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = window.performance.getEntriesByType('navigation')[0]
      
      if (navigation) {
        this.recordMetric('page_load', {
          dns: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcp: navigation.connectEnd - navigation.connectStart,
          request: navigation.responseStart - navigation.requestStart,
          response: navigation.responseEnd - navigation.responseStart,
          dom: navigation.domContentLoadedEventEnd - navigation.responseEnd,
          load: navigation.loadEventEnd - navigation.loadEventStart,
          total: navigation.loadEventEnd - navigation.navigationStart
        })
      }
    }
    // #endif
  }

  // 内存监控
  setupMemoryMonitoring() {
    if (!this.config.enableMemoryMonitoring) return

    setInterval(() => {
      this.collectMemoryMetrics()
    }, 30000) // 30秒收集一次
  }

  // 收集内存指标
  collectMemoryMetrics() {
    // #ifdef H5
    if (typeof window !== 'undefined' && window.performance && window.performance.memory) {
      const memory = window.performance.memory
      
      this.recordMetric('memory_usage', {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        usage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100).toFixed(2)
      })
    }
    // #endif

    // #ifdef APP-PLUS
    // App环境下的内存监控
    if (typeof plus !== 'undefined') {
      plus.runtime.getProperty(plus.runtime.appid, (info) => {
        this.recordMetric('app_memory', {
          memory: info.memory || 0
        })
      })
    }
    // #endif
  }

  // 网络监控
  setupNetworkMonitoring() {
    if (!this.config.enableNetworkMonitoring) return

    // 监控uni.request
    if (typeof uni !== 'undefined') {
      const originalRequest = uni.request
      
      uni.request = (options) => {
        const startTime = Date.now()
        const originalSuccess = options.success
        const originalFail = options.fail
        
        options.success = (res) => {
          this.recordMetric('network_request', {
            url: options.url,
            method: options.method || 'GET',
            duration: Date.now() - startTime,
            status: res.statusCode,
            success: true
          })
          
          if (originalSuccess) originalSuccess(res)
        }
        
        options.fail = (err) => {
          this.recordMetric('network_request', {
            url: options.url,
            method: options.method || 'GET',
            duration: Date.now() - startTime,
            error: err.errMsg,
            success: false
          })
          
          if (originalFail) originalFail(err)
        }
        
        return originalRequest(options)
      }
    }
  }

  // 记录性能指标
  recordMetric(type, data) {
    const metric = {
      type,
      data,
      timestamp: Date.now(),
      id: this.generateId()
    }

    if (!this.metrics.has(type)) {
      this.metrics.set(type, [])
    }

    const typeMetrics = this.metrics.get(type)
    typeMetrics.push(metric)

    // 限制每种类型的指标数量
    if (typeMetrics.length > this.config.maxMetricsCount / 10) {
      typeMetrics.shift()
    }

    // 通知观察者
    this.notifyObservers(metric)
  }

  // 生成ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 添加观察者
  addObserver(observer) {
    this.observers.push(observer)
  }

  // 移除观察者
  removeObserver(observer) {
    const index = this.observers.indexOf(observer)
    if (index > -1) {
      this.observers.splice(index, 1)
    }
  }

  // 通知观察者
  notifyObservers(metric) {
    this.observers.forEach(observer => {
      try {
        observer(metric)
      } catch (error) {
        console.error('性能监控观察者错误:', error)
      }
    })
  }

  // 获取性能统计
  getStats() {
    const stats = {}
    
    this.metrics.forEach((metrics, type) => {
      stats[type] = this.calculateStats(metrics)
    })
    
    return stats
  }

  // 计算统计数据
  calculateStats(metrics) {
    if (metrics.length === 0) return null

    const values = metrics.map(m => {
      if (typeof m.data === 'number') return m.data
      if (m.data.duration) return m.data.duration
      if (m.data.total) return m.data.total
      return 0
    })

    values.sort((a, b) => a - b)

    return {
      count: metrics.length,
      min: Math.min(...values),
      max: Math.max(...values),
      avg: values.reduce((a, b) => a + b, 0) / values.length,
      median: values[Math.floor(values.length / 2)],
      p95: values[Math.floor(values.length * 0.95)],
      p99: values[Math.floor(values.length * 0.99)]
    }
  }

  // 开始定期上报
  startReporting() {
    setInterval(() => {
      this.reportMetrics()
    }, this.config.reportInterval)
  }

  // 上报性能指标
  async reportMetrics() {
    try {
      const stats = this.getStats()
      const report = {
        timestamp: Date.now(),
        stats,
        deviceInfo: this.getDeviceInfo()
      }

      console.log('性能报告:', report)
      
      // 实际项目中应该发送到性能监控服务
      // await this.sendPerformanceReport(report)
      
    } catch (error) {
      console.error('性能上报失败:', error)
    }
  }

  // 获取设备信息
  getDeviceInfo() {
    if (typeof uni !== 'undefined') {
      try {
        const systemInfo = uni.getSystemInfoSync()
        return {
          platform: systemInfo.platform,
          system: systemInfo.system,
          model: systemInfo.model,
          brand: systemInfo.brand,
          screenWidth: systemInfo.screenWidth,
          screenHeight: systemInfo.screenHeight,
          pixelRatio: systemInfo.pixelRatio
        }
      } catch (error) {
        console.error('获取设备信息失败:', error)
      }
    }
    
    return {}
  }

  // 清除指标
  clearMetrics() {
    this.metrics.clear()
  }

  // 获取指定类型的指标
  getMetrics(type) {
    return this.metrics.get(type) || []
  }
}

// 性能计时器
export class PerformanceTimer {
  constructor(name) {
    this.name = name
    this.startTime = null
    this.endTime = null
    this.marks = new Map()
  }

  // 开始计时
  start() {
    this.startTime = Date.now()
    return this
  }

  // 结束计时
  end() {
    this.endTime = Date.now()
    return this.getDuration()
  }

  // 添加标记
  mark(name) {
    this.marks.set(name, Date.now())
    return this
  }

  // 获取持续时间
  getDuration() {
    if (!this.startTime) return 0
    const endTime = this.endTime || Date.now()
    return endTime - this.startTime
  }

  // 获取标记间隔
  getMeasure(startMark, endMark) {
    const startTime = this.marks.get(startMark)
    const endTime = this.marks.get(endMark)
    
    if (!startTime || !endTime) return 0
    return endTime - startTime
  }

  // 获取报告
  getReport() {
    return {
      name: this.name,
      duration: this.getDuration(),
      marks: Object.fromEntries(this.marks),
      startTime: this.startTime,
      endTime: this.endTime
    }
  }
}

// 函数性能装饰器
export function measurePerformance(name) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args) {
      const timer = new PerformanceTimer(`${name || propertyKey}`)
      timer.start()
      
      try {
        const result = await originalMethod.apply(this, args)
        const duration = timer.end()
        
        performanceMonitor.recordMetric('function_performance', {
          name: timer.name,
          duration,
          success: true
        })
        
        return result
      } catch (error) {
        const duration = timer.end()
        
        performanceMonitor.recordMetric('function_performance', {
          name: timer.name,
          duration,
          success: false,
          error: error.message
        })
        
        throw error
      }
    }
    
    return descriptor
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()

// 便捷函数
export function createTimer(name) {
  return new PerformanceTimer(name)
}

export function recordMetric(type, data) {
  performanceMonitor.recordMetric(type, data)
}

export function getPerformanceStats() {
  return performanceMonitor.getStats()
}

// 页面性能监控混入
export const performanceMixin = {
  data() {
    return {
      pageTimer: null
    }
  },
  
  onLoad() {
    this.pageTimer = createTimer(`page_${this.$options.name || 'unknown'}`)
    this.pageTimer.start().mark('load')
  },
  
  onReady() {
    if (this.pageTimer) {
      this.pageTimer.mark('ready')
      const loadDuration = this.pageTimer.getMeasure('load', 'ready')
      
      recordMetric('page_performance', {
        page: this.$options.name || 'unknown',
        loadDuration,
        type: 'ready'
      })
    }
  },
  
  onShow() {
    if (this.pageTimer) {
      this.pageTimer.mark('show')
    }
  },
  
  onUnload() {
    if (this.pageTimer) {
      this.pageTimer.mark('unload')
      const totalDuration = this.pageTimer.getMeasure('load', 'unload')
      
      recordMetric('page_performance', {
        page: this.$options.name || 'unknown',
        totalDuration,
        type: 'unload'
      })
    }
  }
}

export default {
  PerformanceMonitor,
  PerformanceTimer,
  performanceMonitor,
  createTimer,
  recordMetric,
  getPerformanceStats,
  measurePerformance,
  performanceMixin
}
