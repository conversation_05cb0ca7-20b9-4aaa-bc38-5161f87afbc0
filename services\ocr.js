/**
 * OCR识别服务
 */

import { http } from '@/utils/request'

// OCR服务配置
const OCR_CONFIG = {
  // 百度OCR配置
  baidu: {
    apiKey: 'your_baidu_api_key',
    secretKey: 'your_baidu_secret_key',
    url: 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic'
  },
  
  // 腾讯OCR配置
  tencent: {
    secretId: 'your_tencent_secret_id',
    secretKey: 'your_tencent_secret_key',
    region: 'ap-beijing',
    url: 'https://ocr.tencentcloudapi.com'
  }
}

export class OCRService {
  constructor() {
    this.currentProvider = 'mock' // mock, baidu, tencent
    this.accessToken = null
    this.tokenExpiry = null
  }

  // 识别健康报告
  async recognizeHealthReport(imagePath) {
    try {
      uni.showLoading({ title: '识别中...' })
      
      // 图片预处理
      const processedImage = await this.preprocessImage(imagePath)
      
      let result = null
      
      // 根据配置选择OCR服务
      switch (this.currentProvider) {
        case 'baidu':
          result = await this.baiduOCR(processedImage)
          break
        case 'tencent':
          result = await this.tencentOCR(processedImage)
          break
        case 'mock':
        default:
          result = await this.mockOCR(processedImage)
          break
      }
      
      // 解析识别结果
      const parsedResult = this.parseHealthReportResult(result)
      
      uni.hideLoading()
      
      return {
        success: true,
        data: parsedResult,
        rawResult: result
      }
      
    } catch (error) {
      uni.hideLoading()
      console.error('OCR识别失败:', error)
      
      // 尝试降级处理
      return this.handleOCRFailure(error, imagePath)
    }
  }

  // 图片预处理
  async preprocessImage(imagePath) {
    try {
      // 压缩图片
      const compressedPath = await this.compressImage(imagePath)
      
      // 图片质量检查
      const qualityCheck = await this.checkImageQuality(compressedPath)
      if (!qualityCheck.passed) {
        throw new Error(`图片质量不符合要求: ${qualityCheck.issues.join(', ')}`)
      }
      
      return compressedPath
      
    } catch (error) {
      console.error('图片预处理失败:', error)
      throw error
    }
  }

  // 压缩图片
  async compressImage(imagePath, quality = 0.8) {
    return new Promise((resolve, reject) => {
      uni.compressImage({
        src: imagePath,
        quality,
        success: (res) => {
          resolve(res.tempFilePath)
        },
        fail: (error) => {
          console.warn('图片压缩失败，使用原图:', error)
          resolve(imagePath)
        }
      })
    })
  }

  // 检查图片质量
  async checkImageQuality(imagePath) {
    return new Promise((resolve) => {
      uni.getImageInfo({
        src: imagePath,
        success: (res) => {
          const issues = []
          
          // 检查分辨率
          if (res.width < 800 || res.height < 600) {
            issues.push('分辨率过低')
          }
          
          // 检查文件大小
          if (res.path && res.path.length > 10 * 1024 * 1024) {
            issues.push('文件过大')
          }
          
          resolve({
            passed: issues.length === 0,
            issues,
            info: res
          })
        },
        fail: (error) => {
          resolve({
            passed: false,
            issues: ['无法获取图片信息'],
            error
          })
        }
      })
    })
  }

  // 百度OCR识别
  async baiduOCR(imagePath) {
    try {
      // 获取访问令牌
      if (!this.accessToken || Date.now() > this.tokenExpiry) {
        await this.getBaiduAccessToken()
      }
      
      // 将图片转换为Base64
      const base64Image = await this.imageToBase64(imagePath)
      
      // 调用百度OCR API
      const response = await http.post(
        `${OCR_CONFIG.baidu.url}?access_token=${this.accessToken}`,
        {
          image: base64Image,
          language_type: 'CHN_ENG',
          detect_direction: 'true',
          paragraph: 'false',
          probability: 'true'
        }
      )
      
      return response
      
    } catch (error) {
      console.error('百度OCR识别失败:', error)
      throw error
    }
  }

  // 获取百度访问令牌
  async getBaiduAccessToken() {
    try {
      const response = await http.post(
        'https://aip.baidubce.com/oauth/2.0/token',
        {
          grant_type: 'client_credentials',
          client_id: OCR_CONFIG.baidu.apiKey,
          client_secret: OCR_CONFIG.baidu.secretKey
        }
      )
      
      this.accessToken = response.access_token
      this.tokenExpiry = Date.now() + (response.expires_in * 1000)
      
    } catch (error) {
      console.error('获取百度访问令牌失败:', error)
      throw error
    }
  }

  // 腾讯OCR识别
  async tencentOCR(imagePath) {
    // 腾讯OCR实现
    throw new Error('腾讯OCR服务暂未实现')
  }

  // 模拟OCR识别
  async mockOCR(imagePath) {
    // 模拟识别延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 返回模拟数据
    return {
      words_result: [
        { words: '北京协和医院' },
        { words: '体检报告' },
        { words: '姓名：张三' },
        { words: '性别：男' },
        { words: '年龄：35岁' },
        { words: '检查日期：2024-12-15' },
        { words: '血红蛋白 145 g/L 120-160' },
        { words: '白细胞计数 6.5 ×10^9/L 3.5-9.5' },
        { words: '血小板计数 280 ×10^9/L 125-350' },
        { words: '总胆固醇 5.2 mmol/L 3.1-5.7' },
        { words: '甘油三酯 1.8 mmol/L 0.4-1.8' },
        { words: '血糖 5.6 mmol/L 3.9-6.1' },
        { words: '医生：李医生' },
        { words: '报告日期：2024-12-16' }
      ]
    }
  }

  // 解析健康报告结果
  parseHealthReportResult(ocrResult) {
    try {
      const words = ocrResult.words_result || []
      const text = words.map(item => item.words).join('\n')
      
      // 提取基本信息
      const basicInfo = this.extractBasicInfo(text)
      
      // 提取检查项目
      const items = this.extractHealthItems(text)
      
      return {
        basicInfo,
        items,
        originalText: text
      }
      
    } catch (error) {
      console.error('解析OCR结果失败:', error)
      throw error
    }
  }

  // 提取基本信息
  extractBasicInfo(text) {
    const info = {}
    
    // 提取医院名称
    const hospitalMatch = text.match(/([^，。\n]*医院[^，。\n]*)/g)
    if (hospitalMatch) {
      info.hospital = hospitalMatch[0].trim()
    }
    
    // 提取医生姓名
    const doctorMatch = text.match(/医生[：:]\s*([^\s\n]+)/g)
    if (doctorMatch) {
      info.doctor = doctorMatch[0].replace(/医生[：:]\s*/, '').trim()
    }
    
    // 提取检查日期
    const dateMatch = text.match(/检查日期[：:]\s*(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)/g)
    if (dateMatch) {
      const dateStr = dateMatch[0].replace(/检查日期[：:]\s*/, '').trim()
      info.checkDate = this.parseDate(dateStr)
    }
    
    // 提取报告日期
    const reportDateMatch = text.match(/报告日期[：:]\s*(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)/g)
    if (reportDateMatch) {
      const dateStr = reportDateMatch[0].replace(/报告日期[：:]\s*/, '').trim()
      info.reportDate = this.parseDate(dateStr)
    }
    
    return info
  }

  // 提取健康指标
  extractHealthItems(text) {
    const items = []
    const lines = text.split('\n')
    
    // 常见健康指标模式
    const patterns = [
      // 血常规
      /^(血红蛋白|白细胞计数|红细胞计数|血小板计数|血红蛋白浓度)\s+([0-9.]+)\s*([a-zA-Z\/×^0-9]*)\s+([0-9.-]+)/,
      // 生化指标
      /^(总胆固醇|甘油三酯|血糖|尿酸|肌酐|尿素氮)\s+([0-9.]+)\s*([a-zA-Z\/]*)\s+([0-9.-]+)/,
      // 通用模式
      /^([^\d\s]+)\s+([0-9.]+)\s*([a-zA-Z\/×^0-9]*)\s+([0-9.-]+)/
    ]
    
    lines.forEach((line, index) => {
      for (const pattern of patterns) {
        const match = line.trim().match(pattern)
        if (match) {
          const [, name, value, unit, referenceRange] = match
          
          // 判断是否异常
          const isAbnormal = this.checkIfAbnormal(parseFloat(value), referenceRange)
          
          items.push({
            id: `item_${index}`,
            name: name.trim(),
            value: value.trim(),
            unit: unit.trim(),
            referenceRange: referenceRange.trim(),
            isAbnormal,
            category: this.categorizeHealthItem(name.trim())
          })
          
          break
        }
      }
    })
    
    return items
  }

  // 判断指标是否异常
  checkIfAbnormal(value, referenceRange) {
    try {
      // 解析参考范围
      const rangeMatch = referenceRange.match(/([0-9.]+)[-~]([0-9.]+)/)
      if (rangeMatch) {
        const [, min, max] = rangeMatch
        return value < parseFloat(min) || value > parseFloat(max)
      }
      
      // 单边范围
      const lessThanMatch = referenceRange.match(/<\s*([0-9.]+)/)
      if (lessThanMatch) {
        return value >= parseFloat(lessThanMatch[1])
      }
      
      const greaterThanMatch = referenceRange.match(/>\s*([0-9.]+)/)
      if (greaterThanMatch) {
        return value <= parseFloat(greaterThanMatch[1])
      }
      
      return false
      
    } catch (error) {
      console.error('判断异常失败:', error)
      return false
    }
  }

  // 分类健康指标
  categorizeHealthItem(itemName) {
    const categories = {
      '血常规': ['血红蛋白', '白细胞', '红细胞', '血小板', '中性粒细胞', '淋巴细胞'],
      '生化检查': ['总胆固醇', '甘油三酯', '血糖', '尿酸', '肌酐', '尿素氮', '谷丙转氨酶', '谷草转氨酶'],
      '免疫检查': ['乙肝表面抗原', '乙肝表面抗体', '乙肝核心抗体'],
      '尿常规': ['尿蛋白', '尿糖', '尿酮体', '尿比重'],
      '心电图': ['心率', '心律', 'QRS', 'PR间期'],
      '影像检查': ['胸片', 'CT', 'MRI', 'B超']
    }
    
    for (const [category, items] of Object.entries(categories)) {
      if (items.some(item => itemName.includes(item))) {
        return category
      }
    }
    
    return '其他'
  }

  // 解析日期
  parseDate(dateStr) {
    try {
      // 处理中文日期格式
      const normalizedDate = dateStr
        .replace(/年/g, '-')
        .replace(/月/g, '-')
        .replace(/日/g, '')
        .replace(/\s+/g, '')
      
      const date = new Date(normalizedDate)
      return date.toISOString().split('T')[0]
      
    } catch (error) {
      console.error('日期解析失败:', error)
      return new Date().toISOString().split('T')[0]
    }
  }

  // 图片转Base64
  async imageToBase64(imagePath) {
    return new Promise((resolve, reject) => {
      // #ifdef APP-PLUS
      // APP环境下使用plus API
      plus.io.resolveLocalFileSystemURL(imagePath, (entry) => {
        entry.file((file) => {
          const reader = new plus.io.FileReader()
          reader.onloadend = (e) => {
            const base64 = e.target.result.split(',')[1]
            resolve(base64)
          }
          reader.readAsDataURL(file)
        })
      }, reject)
      // #endif
      
      // #ifdef H5
      // H5环境下使用FileReader
      fetch(imagePath)
        .then(res => res.blob())
        .then(blob => {
          const reader = new FileReader()
          reader.onloadend = () => {
            const base64 = reader.result.split(',')[1]
            resolve(base64)
          }
          reader.readAsDataURL(blob)
        })
        .catch(reject)
      // #endif
      
      // #ifdef MP-WEIXIN
      // 小程序环境下使用文件系统API
      uni.getFileSystemManager().readFile({
        filePath: imagePath,
        encoding: 'base64',
        success: (res) => {
          resolve(res.data)
        },
        fail: reject
      })
      // #endif
    })
  }

  // OCR失败处理
  async handleOCRFailure(error, imagePath) {
    console.log('OCR识别失败，启动降级策略:', error.message)
    
    // 策略1: 尝试其他OCR服务
    if (this.currentProvider !== 'mock') {
      try {
        console.log('尝试备用OCR服务')
        const originalProvider = this.currentProvider
        this.currentProvider = 'mock'
        
        const result = await this.recognizeHealthReport(imagePath)
        
        // 恢复原始服务
        this.currentProvider = originalProvider
        
        return result
      } catch (fallbackError) {
        console.error('备用OCR服务也失败:', fallbackError)
      }
    }
    
    // 策略2: 提供手动输入选项
    return {
      success: false,
      error: error.message,
      fallbackOptions: {
        manualInput: true,
        retryOCR: true,
        imageImprovement: true
      },
      suggestions: [
        '请确保图片清晰，光线充足',
        '避免图片模糊或倾斜',
        '可以尝试重新拍照',
        '或选择手动输入数据'
      ]
    }
  }

  // 切换OCR服务提供商
  switchProvider(provider) {
    if (['baidu', 'tencent', 'mock'].includes(provider)) {
      this.currentProvider = provider
      console.log('切换OCR服务到:', provider)
    } else {
      console.error('不支持的OCR服务提供商:', provider)
    }
  }

  // 获取OCR服务状态
  getServiceStatus() {
    return {
      currentProvider: this.currentProvider,
      hasAccessToken: !!this.accessToken,
      tokenExpiry: this.tokenExpiry
    }
  }
}

// 创建OCR服务实例
export const ocrService = new OCRService()

// OCR结果验证器
export class OCRResultValidator {
  // 验证识别结果
  static validate(result) {
    const errors = []
    const warnings = []
    
    // 检查基本信息
    if (!result.basicInfo?.hospital) {
      warnings.push('未识别到医院信息')
    }
    
    if (!result.basicInfo?.checkDate) {
      warnings.push('未识别到检查日期')
    }
    
    // 检查检查项目
    if (!result.items || result.items.length === 0) {
      errors.push('未识别到任何检查项目')
    } else {
      result.items.forEach((item, index) => {
        if (!item.name) {
          errors.push(`第${index + 1}项缺少项目名称`)
        }
        if (!item.value) {
          errors.push(`第${index + 1}项缺少检查结果`)
        }
      })
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: this.calculateScore(result)
    }
  }

  // 计算识别质量分数
  static calculateScore(result) {
    let score = 0
    
    // 基本信息分数 (40分)
    if (result.basicInfo?.hospital) score += 10
    if (result.basicInfo?.doctor) score += 10
    if (result.basicInfo?.checkDate) score += 10
    if (result.basicInfo?.reportDate) score += 10
    
    // 检查项目分数 (60分)
    const itemsScore = Math.min(60, (result.items?.length || 0) * 10)
    score += itemsScore
    
    return Math.min(100, score)
  }
}

export default {
  OCRService,
  ocrService,
  OCRResultValidator
}
