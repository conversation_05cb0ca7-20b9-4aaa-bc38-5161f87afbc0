<template>
  <view class="index-page">
    <view class="container">
      <!-- 启动画面 -->
      <view v-if="isLoading" class="splash-screen">
        <view class="splash-logo">🏥</view>
        <text class="splash-title">健康报告助手</text>
        <view class="loading-indicator">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
      </view>

      <!-- 欢迎界面 -->
      <view v-else-if="showWelcome" class="welcome-screen">
        <view class="welcome-content">
          <view class="welcome-logo">🏥</view>
          <text class="welcome-title">欢迎使用健康报告助手</text>
          <text class="welcome-desc">轻松管理您的健康数据，让健康生活更简单</text>

          <view class="welcome-features">
            <view class="feature-item">
              <text class="feature-icon">📸</text>
              <text class="feature-text">拍照识别</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">📊</text>
              <text class="feature-text">趋势分析</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🔒</text>
              <text class="feature-text">安全保护</text>
            </view>
          </view>

          <view class="welcome-actions">
            <button @click="startApp" class="start-btn">
              开始使用
            </button>
            <text @click="skipWelcome" class="skip-text">跳过</text>
          </view>
        </view>
      </view>

      <!-- 调试信息 -->
      <view v-else class="debug-info">
        <text class="debug-title">Vue3 调试信息</text>
        <text class="debug-text">isLoading: {{ isLoading }}</text>
        <text class="debug-text">showWelcome: {{ showWelcome }}</text>
        <button @click="goToHome" class="debug-btn">跳转到主页</button>
        <button @click="goToTest" class="debug-btn">跳转到测试页</button>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'IndexPage',
  setup() {
    const isLoading = ref(true)
    const showWelcome = ref(false)

    // 页面加载时初始化
    onMounted(() => {
      initApp()
    })

    const initApp = async () => {
      try {
        console.log('开始初始化应用...')

        // 检查是否首次启动
        const isFirstLaunch = !uni.getStorageSync('hasLaunched')
        console.log('是否首次启动:', isFirstLaunch)

        // 模拟加载过程
        await delay(2000)

        isLoading.value = false

        if (isFirstLaunch) {
          showWelcome.value = true
          uni.setStorageSync('hasLaunched', true)
        } else {
          checkLoginStatus()
        }
      } catch (error) {
        console.error('应用初始化失败:', error)
        isLoading.value = false
        showWelcome.value = true
      }
    }

    const checkLoginStatus = () => {
      try {
        const token = uni.getStorageSync('token')
        console.log('检查登录状态, token:', !!token)

        if (token) {
          // 已登录，跳转到首页
          uni.switchTab({
            url: '/pages/home/<USER>'
          })
        } else {
          // 未登录，跳转到登录页
          uni.redirectTo({
            url: '/pages/login/login'
          })
        }
      } catch (error) {
        console.error('检查登录状态失败:', error)
        // 出错时跳转到登录页
        uni.redirectTo({
          url: '/pages/login/login'
        })
      }
    }

    const startApp = () => {
      showWelcome.value = false
      checkLoginStatus()
    }

    const skipWelcome = () => {
      showWelcome.value = false
      checkLoginStatus()
    }

    const goToHome = () => {
      uni.switchTab({
        url: '/pages/home/<USER>'
      })
    }

    const goToTest = () => {
      uni.navigateTo({
        url: '/pages/test/test'
      })
    }

    const delay = (ms) => {
      return new Promise(resolve => setTimeout(resolve, ms))
    }

    return {
      isLoading,
      showWelcome,
      startApp,
      skipWelcome,
      goToHome,
      goToTest
    }
  }
}
</script>

<style lang="scss" scoped>
.index-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  padding: 48rpx 32rpx;
}

.splash-screen {
  text-align: center;
  
  .splash-logo {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 48rpx;
    font-size: 120rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 32rpx;
    margin-left: auto;
    margin-right: auto;
  }
  
  .splash-title {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 96rpx;
  }
  
  .loading-indicator {
    display: flex;
    justify-content: center;
    gap: 16rpx;
    
    .loading-dot {
      width: 16rpx;
      height: 16rpx;
      background-color: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      animation: loading 1.4s infinite ease-in-out;
      
      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.welcome-screen {
  text-align: center;
  color: #ffffff;
  
  .welcome-content {
    .welcome-logo {
      width: 200rpx;
      height: 200rpx;
      font-size: 120rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 32rpx;
      margin: 0 auto 48rpx;
    }
    
    .welcome-title {
      display: block;
      font-size: 48rpx;
      font-weight: 600;
      margin-bottom: 24rpx;
    }
    
    .welcome-desc {
      display: block;
      font-size: 28rpx;
      opacity: 0.9;
      line-height: 1.6;
      margin-bottom: 96rpx;
    }
  }
  
  .welcome-features {
    display: flex;
    justify-content: space-around;
    margin-bottom: 96rpx;
    
    .feature-item {
      text-align: center;
      
      .feature-icon {
        display: block;
        font-size: 64rpx;
        margin-bottom: 16rpx;
        opacity: 0.9;
      }
      
      .feature-text {
        display: block;
        font-size: 24rpx;
        opacity: 0.8;
      }
    }
  }
  
  .welcome-actions {
    .start-btn {
      width: 100%;
      background-color: #ffffff;
      color: var(--primary-color);
      border: none;
      border-radius: 16rpx;
      padding: 32rpx;
      font-size: 32rpx;
      font-weight: 500;
      margin-bottom: 32rpx;
      
      &:active {
        opacity: 0.8;
      }
    }
    
    .skip-text {
      font-size: 28rpx;
      opacity: 0.8;
      text-decoration: underline;
    }
  }
}

.debug-info {
  text-align: center;
  padding: 48rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;

  .debug-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 32rpx;
  }

  .debug-text {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 16rpx;
  }

  .debug-btn {
    background-color: #ffffff;
    color: var(--primary-color);
    border: none;
    border-radius: 12rpx;
    padding: 24rpx 48rpx;
    font-size: 28rpx;
    margin: 16rpx;

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
