<template>
  <view class="reports-page">
    <view class="container">
      <!-- 头部 -->
      <view class="header">
        <text class="title">健康报告</text>
        <view class="add-btn" @click="addReport">
          <text class="iconfont icon-plus"></text>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-label">总报告</text>
          <text class="stat-value">{{ totalReports }}</text>
        </view>
        <view class="stat-card">
          <text class="stat-label">本月新增</text>
          <text class="stat-value">{{ monthlyReports }}</text>
        </view>
        <view class="stat-card">
          <text class="stat-label">待处理</text>
          <text class="stat-value">{{ pendingReports }}</text>
        </view>
      </view>

      <!-- 报告列表 -->
      <view class="reports-section">
        <view class="section-header">
          <text class="section-title">最近报告</text>
          <text class="view-all" @click="viewAllReports">查看全部</text>
        </view>

        <view class="reports-list">
          <view 
            v-for="report in reports" 
            :key="report.id"
            class="report-item"
            @click="viewReport(report)"
          >
            <view class="report-header">
              <view class="report-main">
                <view class="report-icon">
                  <text class="iconfont icon-file-text text-blue"></text>
                </view>
                <view class="report-info">
                  <text class="report-title">{{ report.title }}</text>
                  <view class="report-meta">
                    <text class="iconfont icon-calendar"></text>
                    <text class="meta-text">{{ report.date }}</text>
                    <text class="meta-separator">•</text>
                    <text class="meta-text">{{ report.type }}</text>
                  </view>
                </view>
              </view>
              <text class="iconfont icon-chevron-right arrow"></text>
            </view>
            
            <view class="report-footer">
              <view :class="['status-badge', report.statusColor]">
                <text class="status-text">{{ report.status }}</text>
              </view>
            </view>
            
            <text class="report-summary">{{ report.summary }}</text>
          </view>
        </view>

        <!-- 添加新报告按钮 -->
        <view class="add-report-card" @click="addReport">
          <view class="add-content">
            <text class="iconfont icon-plus"></text>
            <text class="add-text">添加新报告</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ReportsPage',
  data() {
    return {
      totalReports: 12,
      monthlyReports: 3,
      pendingReports: 1,
      reports: [
        {
          id: 1,
          title: '年度体检报告',
          date: '2024-12-15',
          type: '体检报告',
          status: '已完成',
          statusColor: 'status-success',
          summary: '各项指标正常，建议保持良好生活习惯'
        },
        {
          id: 2,
          title: '心电图检查',
          date: '2024-11-28',
          type: '专项检查',
          status: '已完成',
          statusColor: 'status-success',
          summary: '心律正常，无异常发现'
        },
        {
          id: 3,
          title: '血液检查',
          date: '2024-11-20',
          type: '实验室检查',
          status: '待确认',
          statusColor: 'status-warning',
          summary: '部分指标需要医生进一步解读'
        },
        {
          id: 4,
          title: '眼科检查',
          date: '2024-10-15',
          type: '专科检查',
          status: '已完成',
          statusColor: 'status-success',
          summary: '视力稳定，无明显变化'
        }
      ]
    }
  },
  onLoad() {
    this.loadReports()
  },
  methods: {
    loadReports() {
      // 加载报告数据
      console.log('加载报告数据')
    },
    
    addReport() {
      uni.navigateTo({
        url: '/pages/reports/add'
      })
    },
    
    viewReport(report) {
      uni.navigateTo({
        url: `/pages/reports/detail?id=${report.id}`
      })
    },
    
    viewAllReports() {
      // 查看全部报告
      console.log('查看全部报告')
    }
  }
}
</script>

<style lang="scss" scoped>
.reports-page {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.container {
  padding: 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
  
  .title {
    font-size: 48rpx;
    font-weight: 500;
    color: var(--text-color);
  }
  
  .add-btn {
    width: 80rpx;
    height: 80rpx;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .iconfont {
      font-size: 40rpx;
      color: #ffffff;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.stat-card {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  
  .stat-label {
    display: block;
    font-size: 24rpx;
    color: var(--text-secondary);
    margin-bottom: 8rpx;
  }
  
  .stat-value {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    color: var(--text-color);
  }
}

.reports-section {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
    
    .section-title {
      font-size: 36rpx;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .view-all {
      font-size: 28rpx;
      color: var(--primary-color);
    }
  }
}

.reports-list {
  margin-bottom: 48rpx;
}

.report-item {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
  
  .report-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 24rpx;
    
    .report-main {
      display: flex;
      align-items: flex-start;
      flex: 1;
      
      .report-icon {
        width: 80rpx;
        height: 80rpx;
        background-color: rgba(0, 122, 255, 0.1);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        
        .iconfont {
          font-size: 40rpx;
        }
      }
      
      .report-info {
        flex: 1;
        
        .report-title {
          display: block;
          font-size: 32rpx;
          font-weight: 500;
          color: var(--text-color);
          margin-bottom: 8rpx;
        }
        
        .report-meta {
          display: flex;
          align-items: center;
          
          .iconfont {
            font-size: 24rpx;
            color: var(--text-secondary);
            margin-right: 8rpx;
          }
          
          .meta-text {
            font-size: 24rpx;
            color: var(--text-secondary);
          }
          
          .meta-separator {
            margin: 0 12rpx;
            color: var(--text-secondary);
          }
        }
      }
    }
    
    .arrow {
      font-size: 32rpx;
      color: var(--text-secondary);
    }
  }
  
  .report-footer {
    margin-bottom: 16rpx;
  }
  
  .status-badge {
    display: inline-block;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    font-size: 24rpx;
    
    &.status-success {
      background-color: rgba(52, 199, 89, 0.1);
      color: #34C759;
    }
    
    &.status-warning {
      background-color: rgba(255, 149, 0, 0.1);
      color: #FF9500;
    }
    
    &.status-error {
      background-color: rgba(255, 59, 48, 0.1);
      color: #FF3B30;
    }
  }
  
  .report-summary {
    font-size: 28rpx;
    color: var(--text-secondary);
    line-height: 1.5;
  }
}

.add-report-card {
  background-color: var(--card-background);
  border: 4rpx dashed var(--border-color);
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  transition: all 0.3s ease;
  
  &:active {
    background-color: var(--background-color);
  }
  
  .add-content {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .iconfont {
      font-size: 40rpx;
      color: var(--text-secondary);
      margin-right: 16rpx;
    }
    
    .add-text {
      font-size: 32rpx;
      color: var(--text-secondary);
    }
  }
}
</style>
