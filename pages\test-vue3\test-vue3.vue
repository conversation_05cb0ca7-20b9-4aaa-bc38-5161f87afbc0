<template>
  <view class="test-page">
    <view class="header">
      <text class="title">🏥 健康报告助手</text>
      <text class="subtitle">Vue3 + HBuilderX 测试</text>
    </view>
    
    <view class="test-section">
      <view class="test-card">
        <text class="card-title">✅ Vue3 响应式测试</text>
        <text class="count-display">当前计数: {{ count }}</text>
        <button @click="increment" class="test-btn">点击 +1</button>
      </view>
      
      <view class="test-card">
        <text class="card-title">🔧 uni-app API测试</text>
        <button @click="showToast" class="test-btn">显示提示</button>
        <button @click="testStorage" class="test-btn">测试存储</button>
      </view>
      
      <view class="test-card">
        <text class="card-title">🧭 页面导航测试</text>
        <button @click="goToHome" class="nav-btn">进入主页</button>
        <button @click="goToLogin" class="nav-btn">登录页面</button>
      </view>
    </view>
    
    <view class="status-section">
      <text class="status-title">状态信息:</text>
      <text class="status-text">{{ status }}</text>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'TestVue3Page',
  setup() {
    const count = ref(0)
    const status = ref('Vue3 Composition API 已加载')
    
    onMounted(() => {
      console.log('Vue3 测试页面已挂载')
      status.value = '✅ Vue3 + HBuilderX 运行正常'
    })
    
    const increment = () => {
      count.value++
      status.value = `✅ 响应式数据更新成功，计数: ${count.value}`
      console.log('Count updated:', count.value)
    }
    
    const showToast = () => {
      uni.showToast({
        title: 'Vue3 + uni-app 成功!',
        icon: 'success',
        duration: 2000
      })
      status.value = '✅ uni-app API 调用成功'
    }
    
    const testStorage = () => {
      try {
        const testData = {
          timestamp: new Date().toISOString(),
          message: 'Vue3 存储测试',
          count: count.value
        }
        
        uni.setStorageSync('vue3_test', JSON.stringify(testData))
        const stored = uni.getStorageSync('vue3_test')
        
        if (stored) {
          status.value = '✅ 本地存储测试成功'
          uni.showToast({
            title: '存储测试成功',
            icon: 'success'
          })
        }
      } catch (error) {
        status.value = '❌ 存储测试失败: ' + error.message
        console.error('Storage test failed:', error)
      }
    }
    
    const goToHome = () => {
      uni.navigateTo({
        url: '/pages/home/<USER>'
      })
    }
    
    const goToLogin = () => {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    }
    
    return {
      count,
      status,
      increment,
      showToast,
      testStorage,
      goToHome,
      goToLogin
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx;
  color: white;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    opacity: 0.9;
  }
}

.test-section {
  margin-bottom: 60rpx;
}

.test-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
  
  .card-title {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 30rpx;
    text-align: center;
  }
  
  .count-display {
    display: block;
    font-size: 28rpx;
    text-align: center;
    margin-bottom: 30rpx;
    opacity: 0.9;
  }
}

.test-btn, .nav-btn {
  background: white;
  color: #667eea;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  margin: 8rpx;
  font-weight: 500;
  
  &:active {
    opacity: 0.8;
    transform: scale(0.98);
  }
}

.nav-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.status-section {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 16rpx;
  padding: 30rpx;
  
  .status-title {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    margin-bottom: 16rpx;
  }
  
  .status-text {
    display: block;
    font-size: 24rpx;
    opacity: 0.9;
    line-height: 1.5;
  }
}
</style>
