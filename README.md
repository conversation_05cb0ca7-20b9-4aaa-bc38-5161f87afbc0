# 健康报告助手

一个基于 uni-app 开发的个人健康报告管理应用，支持多平台部署（iOS、Android、H5、小程序）。

## 功能特性

### 核心功能
- 📸 **智能拍照识别** - 支持拍照上传健康报告，自动OCR识别数据
- 📊 **数据管理** - 完整的健康报告数据录入、编辑、删除功能
- 📈 **趋势分析** - 健康指标趋势图表，异常数据预警
- 🔒 **安全保护** - 数据加密存储，支持生物识别登录
- 📱 **多端同步** - 支持云端数据同步，多设备访问

### 技术特性
- 🎯 **Vue 3 + Composition API** - 现代化的前端开发体验
- 🗃️ **Pinia 状态管理** - 轻量级、类型安全的状态管理
- 🎨 **响应式设计** - 适配各种屏幕尺寸和设备
- 🔧 **TypeScript 支持** - 类型安全的开发体验
- 📦 **组件化开发** - 可复用的UI组件库
- 🚀 **性能优化** - 懒加载、缓存策略、离线支持

## 项目结构

```
Heath report/
├── components/           # 组件目录
│   ├── common/          # 通用组件
│   │   ├── custom-button.vue
│   │   ├── custom-card.vue
│   │   └── custom-input.vue
│   ├── business/        # 业务组件
│   │   └── camera-upload.vue
│   └── layout/          # 布局组件
├── pages/               # 页面目录
│   ├── index/           # 首页
│   ├── login/           # 登录页
│   ├── home/            # 主页
│   ├── reports/         # 报告管理
│   ├── analysis/        # 数据分析
│   └── profile/         # 个人中心
├── stores/              # 状态管理
│   ├── user.js          # 用户状态
│   ├── report.js        # 报告状态
│   └── app.js           # 应用状态
├── utils/               # 工具函数
│   ├── common.js        # 通用工具
│   ├── request.js       # 网络请求
│   └── security/        # 安全相关
├── services/            # 服务层
│   └── ocr.js           # OCR识别服务
├── styles/              # 样式文件
│   ├── global.scss      # 全局样式
│   └── uni.scss         # uni-app样式变量
├── static/              # 静态资源
├── manifest.json        # 应用配置
├── pages.json           # 页面配置
├── App.vue              # 应用入口
├── main.js              # 主入口文件
└── package.json         # 项目配置
```

## 快速开始

### 环境要求
- Node.js >= 14.0.0
- HBuilderX 3.8.0+ 或 Vue CLI
- 微信开发者工具（小程序开发）
- Android Studio / Xcode（原生App开发）

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

### 开发运行

```bash
# H5 开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# App 开发
npm run dev:app-plus

# 支付宝小程序开发
npm run dev:mp-alipay
```

### 生产构建

```bash
# H5 构建
npm run build:h5

# 微信小程序构建
npm run build:mp-weixin

# App 构建
npm run build:app-plus
```

## 配置说明

### 1. 应用配置 (manifest.json)
- 应用名称、版本号、图标等基本信息
- 各平台特定配置（权限、模块等）
- 第三方SDK配置

### 2. 页面配置 (pages.json)
- 页面路由配置
- 导航栏样式
- 底部tabBar配置
- 全局样式设置

### 3. 环境变量
创建 `.env` 文件配置环境变量：

```env
# API 基础地址
VUE_APP_API_BASE_URL=https://api.healthreport.com

# OCR 服务配置
VUE_APP_OCR_PROVIDER=baidu
VUE_APP_BAIDU_API_KEY=your_api_key
VUE_APP_BAIDU_SECRET_KEY=your_secret_key

# 其他配置
VUE_APP_VERSION=1.0.0
```

## 核心功能详解

### 1. OCR 识别
- 支持百度、腾讯等多家OCR服务
- 智能识别健康报告中的关键信息
- 支持手动校正和编辑
- 识别结果验证和质量评估

### 2. 数据管理
- 本地存储 + 云端同步
- 数据加密保护
- 离线模式支持
- 数据导入导出

### 3. 趋势分析
- 健康指标趋势图表
- 异常数据预警
- 个性化健康建议
- 数据统计报告

### 4. 安全保护
- 数据加密存储
- 生物识别登录
- 设备指纹验证
- 安全日志记录

## 部署指南

### H5 部署
1. 运行 `npm run build:h5`
2. 将 `dist/build/h5` 目录部署到Web服务器

### 小程序部署
1. 运行对应平台的构建命令
2. 使用对应的开发者工具打开构建后的目录
3. 上传代码并提交审核

### App 部署
1. 运行 `npm run build:app-plus`
2. 使用 HBuilderX 打包为原生App
3. 发布到应用商店

## 开发规范

### 代码规范
- 使用 ESLint + Prettier 进行代码格式化
- 遵循 Vue 3 Composition API 最佳实践
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 组件开发
- 组件应该是可复用的
- 提供完整的 props 类型定义
- 包含必要的事件和插槽
- 编写组件文档和使用示例

## 性能优化

### 1. 包体积优化
- 按需引入第三方库
- 图片资源压缩
- 代码分割和懒加载
- Tree Shaking 移除无用代码

### 2. 运行时优化
- 虚拟列表处理大数据
- 图片懒加载
- 请求缓存和防抖
- 内存泄漏防护

### 3. 用户体验优化
- 骨架屏加载
- 错误边界处理
- 离线模式支持
- 渐进式Web应用(PWA)

## 常见问题

### Q: 如何配置OCR服务？
A: 在 `services/ocr.js` 中配置对应的API密钥，支持百度、腾讯等多家服务商。

### Q: 如何自定义主题？
A: 修改 `styles/global.scss` 中的CSS变量，或在 `uni.scss` 中定义新的样式变量。

### Q: 如何添加新的健康指标类型？
A: 在 `services/ocr.js` 的 `categorizeHealthItem` 方法中添加新的分类规则。

### Q: 如何处理数据同步冲突？
A: 应用采用"最后写入获胜"策略，同时提供手动解决冲突的界面。

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页: [GitHub Repository](https://github.com/your-username/health-report-app)
- 问题反馈: [Issues](https://github.com/your-username/health-report-app/issues)
- 邮箱: <EMAIL>

## 更新日志

### v1.0.0 (2024-12-16)
- 🎉 初始版本发布
- ✨ 支持健康报告OCR识别
- ✨ 完整的数据管理功能
- ✨ 趋势分析和数据可视化
- ✨ 多平台支持（iOS、Android、H5、小程序）
- 🔒 数据安全和隐私保护
- 📱 响应式设计和用户体验优化
