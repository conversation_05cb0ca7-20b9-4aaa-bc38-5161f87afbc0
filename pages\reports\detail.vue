<template>
  <view class="report-detail-page">
    <view class="container">
      <!-- 报告头部信息 -->
      <custom-card class="report-header" shadow="medium">
        <view class="header-content">
          <view class="report-info">
            <text class="report-title">{{ report?.title || '报告详情' }}</text>
            <view class="report-meta">
              <view class="meta-item">
                <text class="iconfont icon-hospital meta-icon"></text>
                <text class="meta-text">{{ report?.hospital || '未知医院' }}</text>
              </view>
              <view class="meta-item" v-if="report?.doctor">
                <text class="iconfont icon-user meta-icon"></text>
                <text class="meta-text">{{ report.doctor }}</text>
              </view>
              <view class="meta-item">
                <text class="iconfont icon-calendar meta-icon"></text>
                <text class="meta-text">{{ formatDate(report?.checkDate) }}</text>
              </view>
            </view>
          </view>
          
          <view class="report-actions">
            <view class="action-btn" @click="shareReport">
              <text class="iconfont icon-share"></text>
            </view>
            <view class="action-btn" @click="editReport">
              <text class="iconfont icon-edit"></text>
            </view>
            <view class="action-btn" @click="deleteReport">
              <text class="iconfont icon-delete"></text>
            </view>
          </view>
        </view>
      </custom-card>

      <!-- 原始图片 -->
      <custom-card v-if="report?.originalImage" title="原始报告" class="image-section">
        <view class="image-container" @click="previewImage">
          <image 
            :src="report.originalImage" 
            class="report-image" 
            mode="aspectFit"
          />
          <view class="image-overlay">
            <text class="iconfont icon-zoom-in"></text>
            <text class="overlay-text">点击查看大图</text>
          </view>
        </view>
      </custom-card>

      <!-- 检查项目 -->
      <custom-card title="检查项目" class="items-section">
        <view class="items-list">
          <view 
            v-for="item in report?.items || []" 
            :key="item.id"
            class="health-item"
          >
            <view class="item-header">
              <text class="item-name">{{ item.name }}</text>
              <view v-if="item.isAbnormal" class="abnormal-badge">
                <text class="badge-text">异常</text>
              </view>
            </view>
            
            <view class="item-content">
              <view class="item-value-section">
                <text class="value-label">检查结果</text>
                <text class="item-value">{{ item.value }} {{ item.unit }}</text>
              </view>
              
              <view class="item-reference-section">
                <text class="reference-label">参考范围</text>
                <text class="item-reference">{{ item.referenceRange }}</text>
              </view>
              
              <view class="item-category">
                <text class="category-label">分类：</text>
                <text class="category-value">{{ item.category }}</text>
              </view>
            </view>
          </view>
        </view>
      </custom-card>

      <!-- 备注信息 -->
      <custom-card v-if="report?.notes" title="备注" class="notes-section">
        <text class="notes-text">{{ report.notes }}</text>
      </custom-card>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <custom-button 
          type="secondary" 
          icon="icon-chart"
          @click="viewTrend"
        >
          查看趋势
        </custom-button>
        <custom-button 
          type="primary" 
          icon="icon-download"
          @click="exportReport"
        >
          导出报告
        </custom-button>
      </view>
    </view>
  </view>
</template>

<script>
import { useReportStore } from '@/stores/report'
import { formatDate } from '@/utils/common'
import CustomCard from '@/components/common/custom-card.vue'
import CustomButton from '@/components/common/custom-button.vue'

export default {
  name: 'ReportDetailPage',
  components: {
    CustomCard,
    CustomButton
  },
  
  data() {
    return {
      reportId: '',
      report: null,
      loading: false
    }
  },
  
  onLoad(options) {
    this.reportId = options.id
    this.loadReportDetail()
  },
  
  methods: {
    // 加载报告详情
    async loadReportDetail() {
      if (!this.reportId) {
        uni.showToast({
          title: '报告ID无效',
          icon: 'none'
        })
        return
      }
      
      try {
        this.loading = true
        const reportStore = useReportStore()
        this.report = await reportStore.getReportDetail(this.reportId)
        
        if (!this.report) {
          throw new Error('报告不存在')
        }
        
      } catch (error) {
        console.error('加载报告详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
        
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
        
      } finally {
        this.loading = false
      }
    },
    
    // 格式化日期
    formatDate(date) {
      return formatDate(date, 'YYYY-MM-DD')
    },
    
    // 预览图片
    previewImage() {
      if (this.report?.originalImage) {
        uni.previewImage({
          urls: [this.report.originalImage],
          current: this.report.originalImage
        })
      }
    },
    
    // 分享报告
    shareReport() {
      // #ifdef MP-WEIXIN
      uni.showShareMenu({
        withShareTicket: true,
        success: () => {
          console.log('分享菜单显示成功')
        }
      })
      // #endif
      
      // #ifdef APP-PLUS || H5
      uni.showActionSheet({
        itemList: ['复制链接', '保存图片', '发送给朋友'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.copyReportLink()
              break
            case 1:
              this.saveReportImage()
              break
            case 2:
              this.sendToFriend()
              break
          }
        }
      })
      // #endif
    },
    
    // 复制报告链接
    copyReportLink() {
      const link = `https://app.healthreport.com/report/${this.reportId}`
      uni.setClipboardData({
        data: link,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          })
        }
      })
    },
    
    // 保存报告图片
    saveReportImage() {
      if (!this.report?.originalImage) {
        uni.showToast({
          title: '没有可保存的图片',
          icon: 'none'
        })
        return
      }
      
      uni.saveImageToPhotosAlbum({
        filePath: this.report.originalImage,
        success: () => {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        },
        fail: () => {
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 编辑报告
    editReport() {
      uni.navigateTo({
        url: `/pages/reports/edit?id=${this.reportId}`
      })
    },
    
    // 删除报告
    deleteReport() {
      uni.showModal({
        title: '确认删除',
        content: '删除后无法恢复，确定要删除这份报告吗？',
        confirmColor: '#FF3B30',
        success: async (res) => {
          if (res.confirm) {
            try {
              const reportStore = useReportStore()
              await reportStore.deleteReport(this.reportId)
              
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              
              setTimeout(() => {
                uni.navigateBack()
              }, 1000)
              
            } catch (error) {
              console.error('删除报告失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 查看趋势
    viewTrend() {
      uni.navigateTo({
        url: `/pages/analysis/trend?reportId=${this.reportId}`
      })
    },
    
    // 导出报告
    async exportReport() {
      try {
        uni.showLoading({ title: '导出中...' })
        
        const reportStore = useReportStore()
        await reportStore.exportReports('json')
        
        uni.hideLoading()
        
      } catch (error) {
        uni.hideLoading()
        console.error('导出失败:', error)
        uni.showToast({
          title: '导出失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.report-detail-page {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.container {
  padding: 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.report-header {
  margin-bottom: 32rpx;
  
  .header-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    
    .report-info {
      flex: 1;
      
      .report-title {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 16rpx;
      }
      
      .report-meta {
        .meta-item {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .meta-icon {
            font-size: 24rpx;
            color: var(--text-secondary);
            margin-right: 12rpx;
            width: 24rpx;
          }
          
          .meta-text {
            font-size: 26rpx;
            color: var(--text-secondary);
          }
        }
      }
    }
    
    .report-actions {
      display: flex;
      gap: 16rpx;
      
      .action-btn {
        width: 64rpx;
        height: 64rpx;
        background-color: var(--background-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .iconfont {
          font-size: 28rpx;
          color: var(--text-secondary);
        }
        
        &:active {
          background-color: var(--border-color);
        }
      }
    }
  }
}

.image-section {
  margin-bottom: 32rpx;
  
  .image-container {
    position: relative;
    border-radius: 12rpx;
    overflow: hidden;
    
    .report-image {
      width: 100%;
      height: 400rpx;
      background-color: var(--background-color);
    }
    
    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      
      .iconfont {
        font-size: 48rpx;
        color: #ffffff;
        margin-bottom: 8rpx;
      }
      
      .overlay-text {
        font-size: 24rpx;
        color: #ffffff;
      }
    }
    
    &:active .image-overlay {
      opacity: 1;
    }
  }
}

.items-section {
  margin-bottom: 32rpx;
}

.health-item {
  background-color: var(--background-color);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
    
    .item-name {
      font-size: 30rpx;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .abnormal-badge {
      background-color: var(--error-color);
      color: #ffffff;
      padding: 6rpx 12rpx;
      border-radius: 6rpx;
      font-size: 20rpx;
      
      .badge-text {
        font-size: 20rpx;
      }
    }
  }
  
  .item-content {
    .item-value-section,
    .item-reference-section {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      
      .value-label,
      .reference-label {
        font-size: 24rpx;
        color: var(--text-secondary);
        min-width: 120rpx;
      }
      
      .item-value {
        font-size: 28rpx;
        font-weight: 500;
        color: var(--text-color);
      }
      
      .item-reference {
        font-size: 26rpx;
        color: var(--text-secondary);
      }
    }
    
    .item-category {
      display: flex;
      align-items: center;
      
      .category-label {
        font-size: 24rpx;
        color: var(--text-secondary);
      }
      
      .category-value {
        font-size: 24rpx;
        color: var(--primary-color);
        background-color: rgba(0, 122, 255, 0.1);
        padding: 4rpx 12rpx;
        border-radius: 6rpx;
        margin-left: 8rpx;
      }
    }
  }
}

.notes-section {
  margin-bottom: 32rpx;
  
  .notes-text {
    font-size: 28rpx;
    color: var(--text-color);
    line-height: 1.6;
  }
}

.action-buttons {
  display: flex;
  gap: 24rpx;
  
  .custom-button {
    flex: 1;
  }
}
</style>
