<template>
  <view :class="wrapperClass">
    <!-- 标签 -->
    <text v-if="label" class="input-label">{{ label }}</text>
    
    <!-- 输入框容器 -->
    <view :class="inputWrapperClass">
      <!-- 前置图标 -->
      <text 
        v-if="prefixIcon" 
        :class="['iconfont', prefixIcon, 'input-prefix-icon']"
      ></text>
      
      <!-- 输入框 -->
      <input
        v-if="type !== 'textarea'"
        :type="inputType"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :focus="focus"
        :password="type === 'password' && !showPassword"
        :class="inputClass"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleConfirm"
      />
      
      <!-- 文本域 -->
      <textarea
        v-else
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :focus="focus"
        :auto-height="autoHeight"
        :class="inputClass"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      ></textarea>
      
      <!-- 密码显示切换 -->
      <text 
        v-if="type === 'password'"
        :class="['iconfont', showPassword ? 'icon-eye' : 'icon-eye-off', 'password-toggle']"
        @click="togglePassword"
      ></text>
      
      <!-- 清除按钮 -->
      <text 
        v-if="clearable && modelValue && !disabled"
        class="iconfont icon-close-circle input-clear"
        @click="clearInput"
      ></text>
      
      <!-- 后置图标 -->
      <text 
        v-if="suffixIcon" 
        :class="['iconfont', suffixIcon, 'input-suffix-icon']"
        @click="handleSuffixClick"
      ></text>
      
      <!-- 后置按钮 -->
      <view v-if="$slots.suffix" class="input-suffix">
        <slot name="suffix"></slot>
      </view>
    </view>
    
    <!-- 错误信息 -->
    <text v-if="errorMessage" class="input-error">{{ errorMessage }}</text>
    
    <!-- 帮助信息 -->
    <text v-if="helpText" class="input-help">{{ helpText }}</text>
  </view>
</template>

<script>
export default {
  name: 'CustomInput',
  props: {
    // v-model绑定值
    modelValue: {
      type: [String, Number],
      default: ''
    },
    
    // 输入框类型
    type: {
      type: String,
      default: 'text', // text, number, password, textarea, phone, email
      validator: (value) => {
        return ['text', 'number', 'password', 'textarea', 'phone', 'email'].includes(value)
      }
    },
    
    // 标签
    label: {
      type: String,
      default: ''
    },
    
    // 占位符
    placeholder: {
      type: String,
      default: ''
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    
    // 最大长度
    maxlength: {
      type: Number,
      default: -1
    },
    
    // 是否自动获取焦点
    focus: {
      type: Boolean,
      default: false
    },
    
    // 是否可清除
    clearable: {
      type: Boolean,
      default: false
    },
    
    // 前置图标
    prefixIcon: {
      type: String,
      default: ''
    },
    
    // 后置图标
    suffixIcon: {
      type: String,
      default: ''
    },
    
    // 输入框大小
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: (value) => {
        return ['small', 'medium', 'large'].includes(value)
      }
    },
    
    // 是否显示边框
    border: {
      type: Boolean,
      default: true
    },
    
    // 是否圆角
    rounded: {
      type: Boolean,
      default: true
    },
    
    // 错误信息
    errorMessage: {
      type: String,
      default: ''
    },
    
    // 帮助信息
    helpText: {
      type: String,
      default: ''
    },
    
    // 是否必填
    required: {
      type: Boolean,
      default: false
    },
    
    // 自动高度（仅textarea）
    autoHeight: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      showPassword: false,
      isFocused: false
    }
  },
  
  computed: {
    wrapperClass() {
      const classes = ['custom-input-wrapper']
      
      if (this.disabled) classes.push('input-disabled')
      if (this.errorMessage) classes.push('input-error-state')
      if (this.isFocused) classes.push('input-focused')
      
      return classes.join(' ')
    },
    
    inputWrapperClass() {
      const classes = ['input-container']
      
      classes.push(`input-size-${this.size}`)
      
      if (this.border) classes.push('input-border')
      if (this.rounded) classes.push('input-rounded')
      if (this.disabled) classes.push('input-container-disabled')
      if (this.isFocused) classes.push('input-container-focused')
      if (this.errorMessage) classes.push('input-container-error')
      
      return classes.join(' ')
    },
    
    inputClass() {
      return ['input-field']
    },
    
    inputType() {
      const typeMap = {
        phone: 'number',
        email: 'text',
        password: 'text',
        textarea: 'text'
      }
      
      return typeMap[this.type] || this.type
    }
  },
  
  methods: {
    handleInput(event) {
      let value = event.detail.value
      
      // 类型验证和格式化
      if (this.type === 'phone') {
        value = value.replace(/\D/g, '').slice(0, 11)
      } else if (this.type === 'number') {
        value = value.replace(/[^\d.]/g, '')
      }
      
      this.$emit('update:modelValue', value)
      this.$emit('input', value)
    },
    
    handleFocus(event) {
      this.isFocused = true
      this.$emit('focus', event)
    },
    
    handleBlur(event) {
      this.isFocused = false
      this.$emit('blur', event)
      
      // 验证输入
      this.validateInput()
    },
    
    handleConfirm(event) {
      this.$emit('confirm', event)
    },
    
    handleSuffixClick(event) {
      this.$emit('suffix-click', event)
    },
    
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    clearInput() {
      this.$emit('update:modelValue', '')
      this.$emit('clear')
    },
    
    // 输入验证
    validateInput() {
      if (!this.modelValue && this.required) {
        this.$emit('error', '此字段为必填项')
        return false
      }
      
      if (this.type === 'phone' && this.modelValue) {
        const phoneRegex = /^1[3-9]\d{9}$/
        if (!phoneRegex.test(this.modelValue)) {
          this.$emit('error', '请输入正确的手机号')
          return false
        }
      }
      
      if (this.type === 'email' && this.modelValue) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(this.modelValue)) {
          this.$emit('error', '请输入正确的邮箱地址')
          return false
        }
      }
      
      this.$emit('error', '')
      return true
    },
    
    // 获取焦点
    focus() {
      // 由于uni-app限制，无法直接调用focus方法
      // 可以通过设置focus属性来实现
      this.$emit('update:focus', true)
    },
    
    // 失去焦点
    blur() {
      this.$emit('update:focus', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-input-wrapper {
  margin-bottom: 24rpx;
  
  &.input-disabled {
    opacity: 0.6;
  }
}

.input-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16rpx;
  
  &::after {
    content: ' *';
    color: var(--error-color);
    display: none;
  }
}

.custom-input-wrapper.input-required .input-label::after {
  display: inline;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--background-color);
  transition: all 0.3s ease;
  
  // 大小样式
  &.input-size-small {
    padding: 16rpx 20rpx;
    min-height: 64rpx;
  }
  
  &.input-size-medium {
    padding: 20rpx 24rpx;
    min-height: 80rpx;
  }
  
  &.input-size-large {
    padding: 24rpx 28rpx;
    min-height: 96rpx;
  }
  
  // 边框样式
  &.input-border {
    border: 2rpx solid var(--border-color);
  }
  
  // 圆角样式
  &.input-rounded {
    border-radius: 12rpx;
  }
  
  // 状态样式
  &.input-container-focused {
    border-color: var(--primary-color);
    background-color: #ffffff;
    box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
  }
  
  &.input-container-error {
    border-color: var(--error-color);
    background-color: rgba(255, 59, 48, 0.05);
  }
  
  &.input-container-disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
}

.input-field {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-color);
  background: transparent;
  border: none;
  outline: none;
  
  &::placeholder {
    color: var(--text-secondary);
  }
}

.input-prefix-icon,
.input-suffix-icon {
  font-size: 32rpx;
  color: var(--text-secondary);
}

.input-prefix-icon {
  margin-right: 16rpx;
}

.input-suffix-icon {
  margin-left: 16rpx;
}

.password-toggle {
  margin-left: 16rpx;
  font-size: 32rpx;
  color: var(--text-secondary);
  cursor: pointer;
  
  &:active {
    color: var(--primary-color);
  }
}

.input-clear {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  cursor: pointer;
  
  &:active {
    color: var(--error-color);
  }
}

.input-suffix {
  margin-left: 16rpx;
}

.input-error {
  display: block;
  font-size: 24rpx;
  color: var(--error-color);
  margin-top: 8rpx;
  line-height: 1.4;
}

.input-help {
  display: block;
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: 8rpx;
  line-height: 1.4;
}

// 文本域特殊样式
.input-container:has(textarea) {
  align-items: flex-start;
  
  .input-field {
    min-height: 120rpx;
    resize: none;
  }
}
</style>
