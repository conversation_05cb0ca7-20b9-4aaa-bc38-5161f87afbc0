# 🔄 HBuilderX 项目迁移指南

## 📋 迁移步骤详解

### 第一步：创建HBuilderX项目（已完成）
✅ 您已经下载安装了HBuilderX

### 第二步：创建新项目
1. 打开HBuilderX
2. 文件 → 新建 → 项目
3. 选择 `uni-app` → `Vue3/Vite版`
4. 项目名称：`健康报告助手`
5. 位置：`E:\健康报告助手`（新项目）
6. 点击创建

### 第三步：验证基础项目
1. 在HBuilderX中点击 `运行` → `运行到浏览器` → `Chrome`
2. 确认看到默认的uni-app页面（不是空白）

### 第四步：迁移文件

#### 4.1 需要复制的文件夹（从旧项目到新项目）

**从 `E:\Heath report\` 复制到 `E:\健康报告助手\`：**

1. **pages文件夹**
   ```
   复制：E:\Heath report\pages\
   到：  E:\健康报告助手\pages\
   
   包含的页面：
   - home/home.vue (主页)
   - login/login.vue (登录页)
   - reports/ (报告相关页面)
   - analysis/analysis.vue (数据分析)
   - profile/profile.vue (个人中心)
   - camera/camera.vue (拍照识别)
   ```

2. **components文件夹**
   ```
   复制：E:\Heath report\components\
   到：  E:\健康报告助手\components\
   
   包含：
   - common/ (通用组件)
   - business/ (业务组件)
   - layout/ (布局组件)
   ```

3. **stores文件夹**
   ```
   复制：E:\Heath report\stores\
   到：  E:\健康报告助手\stores\
   
   包含：
   - user.js (用户状态管理)
   - app.js (应用状态管理)
   - report.js (报告状态管理)
   ```

4. **utils文件夹**
   ```
   复制：E:\Heath report\utils\
   到：  E:\健康报告助手\utils\
   
   包含：
   - common.js (通用工具)
   - request.js (网络请求)
   - error-handler.js (错误处理)
   ```

5. **services文件夹**
   ```
   复制：E:\Heath report\services\
   到：  E:\健康报告助手\services\
   
   包含：
   - ocr.js (OCR识别服务)
   ```

6. **styles文件夹**
   ```
   复制：E:\Heath report\styles\
   到：  E:\健康报告助手\styles\
   
   包含：
   - global.scss (全局样式)
   ```

#### 4.2 需要替换的配置文件

1. **pages.json**
   - 用我们的 `pages.json` 替换新项目的 `pages.json`

2. **manifest.json**
   - 用我们的 `manifest.json` 替换新项目的 `manifest.json`

### 第五步：安装额外依赖

在HBuilderX的终端中运行：
```bash
npm install pinia
```

### 第六步：修改main.js

将新项目的 `main.js` 修改为：
```javascript
import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

export function createApp() {
  const app = createSSRApp(App)
  
  // 添加Pinia状态管理
  const pinia = createPinia()
  app.use(pinia)
  
  return {
    app,
    pinia
  }
}
```

### 第七步：测试运行

1. **运行项目**
   - 在HBuilderX中点击 `运行` → `运行到浏览器` → `Chrome`

2. **验证功能**
   - 确认页面不再空白
   - 测试页面导航
   - 检查控制台无错误

## 🎯 迁移后的项目结构

```
E:\健康报告助手\
├── pages/
│   ├── index/index.vue (首页)
│   ├── home/home.vue (主页)
│   ├── login/login.vue (登录)
│   ├── reports/ (报告页面)
│   ├── analysis/analysis.vue (分析)
│   ├── profile/profile.vue (个人中心)
│   └── camera/camera.vue (拍照)
├── components/ (组件)
├── stores/ (状态管理)
├── utils/ (工具函数)
├── services/ (服务)
├── styles/ (样式)
├── static/ (静态资源)
├── main.js
├── App.vue
├── pages.json
├── manifest.json
└── package.json
```

## ⚠️ 注意事项

1. **不要复制以下文件**（让HBuilderX自动生成）：
   - `node_modules/`
   - `unpackage/`
   - `package-lock.json`

2. **Vue3语法检查**：
   - 确保所有页面使用Vue3的Composition API
   - 检查store文件的Pinia语法

3. **路径检查**：
   - 确认所有import路径正确
   - 检查静态资源路径

## 🚀 完成后的优势

- ✅ 零配置问题
- ✅ 完整的开发工具
- ✅ 一键运行到多平台
- ✅ 自动热重载
- ✅ 内置调试工具
- ✅ 官方支持和更新

请按照这个指南操作，如果在任何步骤遇到问题，请告诉我具体在哪一步，我会提供详细的帮助！
