/**
 * 通用工具函数
 */

// 生成唯一ID
export function generateId() {
  return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 格式化日期
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return ''
  
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

// 格式化相对时间
export function formatRelativeTime(date) {
  if (!date) return ''
  
  const now = new Date()
  const target = new Date(date)
  const diff = now - target
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前'
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前'
  } else if (diff < month) {
    return Math.floor(diff / day) + '天前'
  } else if (diff < year) {
    return Math.floor(diff / month) + '个月前'
  } else {
    return Math.floor(diff / year) + '年前'
  }
}

// 验证手机号
export function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// 验证邮箱
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证身份证号
export function validateIdCard(idCard) {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

// 深拷贝
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj)
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

// 防抖函数
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func(...args)
  }
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 数字格式化
export function formatNumber(num, decimals = 2) {
  if (isNaN(num)) return '0'
  return Number(num).toFixed(decimals)
}

// 文件大小格式化
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 颜色工具
export const colorUtils = {
  // 十六进制转RGB
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  },
  
  // RGB转十六进制
  rgbToHex(r, g, b) {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
  },
  
  // 获取对比色
  getContrastColor(hex) {
    const rgb = this.hexToRgb(hex)
    if (!rgb) return '#000000'
    
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000
    return brightness > 128 ? '#000000' : '#ffffff'
  }
}

// 数组工具
export const arrayUtils = {
  // 数组去重
  unique(arr, key) {
    if (!key) {
      return [...new Set(arr)]
    }
    
    const seen = new Set()
    return arr.filter(item => {
      const value = item[key]
      if (seen.has(value)) {
        return false
      }
      seen.add(value)
      return true
    })
  },
  
  // 数组分组
  groupBy(arr, key) {
    return arr.reduce((groups, item) => {
      const group = item[key]
      if (!groups[group]) {
        groups[group] = []
      }
      groups[group].push(item)
      return groups
    }, {})
  },
  
  // 数组排序
  sortBy(arr, key, order = 'asc') {
    return arr.sort((a, b) => {
      const aVal = a[key]
      const bVal = b[key]
      
      if (order === 'desc') {
        return bVal > aVal ? 1 : bVal < aVal ? -1 : 0
      } else {
        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0
      }
    })
  }
}

// 存储工具
export const storageUtils = {
  // 设置存储（带过期时间）
  set(key, value, expiry = null) {
    const data = {
      value,
      expiry: expiry ? Date.now() + expiry : null
    }
    uni.setStorageSync(key, JSON.stringify(data))
  },
  
  // 获取存储
  get(key) {
    try {
      const dataStr = uni.getStorageSync(key)
      if (!dataStr) return null
      
      const data = JSON.parse(dataStr)
      
      // 检查是否过期
      if (data.expiry && Date.now() > data.expiry) {
        uni.removeStorageSync(key)
        return null
      }
      
      return data.value
    } catch (error) {
      console.error('获取存储数据失败:', error)
      return null
    }
  },
  
  // 移除存储
  remove(key) {
    uni.removeStorageSync(key)
  },
  
  // 清空存储
  clear() {
    uni.clearStorageSync()
  }
}

// URL工具
export const urlUtils = {
  // 解析查询参数
  parseQuery(url) {
    const query = {}
    const queryString = url.split('?')[1]
    
    if (queryString) {
      queryString.split('&').forEach(param => {
        const [key, value] = param.split('=')
        query[decodeURIComponent(key)] = decodeURIComponent(value || '')
      })
    }
    
    return query
  },
  
  // 构建查询字符串
  buildQuery(params) {
    return Object.keys(params)
      .filter(key => params[key] !== undefined && params[key] !== null)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
  }
}

// 平台检测
export const platformUtils = {
  // 是否为iOS
  isIOS() {
    // #ifdef APP-PLUS
    return uni.getSystemInfoSync().platform === 'ios'
    // #endif
    
    // #ifdef H5
    return /iPad|iPhone|iPod/.test(navigator.userAgent)
    // #endif
    
    return false
  },
  
  // 是否为Android
  isAndroid() {
    // #ifdef APP-PLUS
    return uni.getSystemInfoSync().platform === 'android'
    // #endif
    
    // #ifdef H5
    return /Android/.test(navigator.userAgent)
    // #endif
    
    return false
  },
  
  // 是否为微信小程序
  isWeChat() {
    // #ifdef MP-WEIXIN
    return true
    // #endif
    
    return false
  },
  
  // 获取平台信息
  getPlatformInfo() {
    const systemInfo = uni.getSystemInfoSync()
    return {
      platform: systemInfo.platform,
      system: systemInfo.system,
      version: systemInfo.version,
      model: systemInfo.model,
      brand: systemInfo.brand
    }
  }
}

// 默认导出
export default {
  generateId,
  formatDate,
  formatRelativeTime,
  validatePhone,
  validateEmail,
  validateIdCard,
  deepClone,
  debounce,
  throttle,
  formatNumber,
  formatFileSize,
  colorUtils,
  arrayUtils,
  storageUtils,
  urlUtils,
  platformUtils
}
