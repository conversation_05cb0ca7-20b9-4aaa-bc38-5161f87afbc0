<template>
  <view class="analysis-page">
    <view class="container">
      <!-- 头部 -->
      <view class="header">
        <text class="title">数据分析</text>
        <view class="time-selector" @click="showTimeSelector">
          <text class="time-text">{{ selectedTimeRange }}</text>
          <text class="iconfont icon-chevron-down"></text>
        </view>
      </view>

      <!-- 概览卡片 -->
      <view class="overview-cards">
        <view class="overview-card">
          <text class="overview-title">总体评估</text>
          <text class="overview-value good">良好</text>
          <text class="overview-desc">大部分指标正常</text>
        </view>
        <view class="overview-card">
          <text class="overview-title">异常指标</text>
          <text class="overview-value warning">3项</text>
          <text class="overview-desc">需要关注</text>
        </view>
      </view>

      <!-- 趋势图表 */
      <view class="chart-section">
        <text class="section-title">健康趋势</text>
        
        <!-- 图表选择器 -->
        <view class="chart-tabs">
          <view 
            v-for="tab in chartTabs" 
            :key="tab.key"
            :class="['chart-tab', { active: activeChart === tab.key }]"
            @click="switchChart(tab.key)"
          >
            <text class="tab-text">{{ tab.label }}</text>
          </view>
        </view>

        <!-- 图表容器 -->
        <view class="chart-container">
          <canvas 
            canvas-id="trendChart" 
            id="trendChart"
            class="chart-canvas"
            @touchstart="chartTouchStart"
            @touchmove="chartTouchMove"
            @touchend="chartTouchEnd"
          ></canvas>
        </view>
      </view>

      <!-- 异常指标列表 -->
      <view class="abnormal-section">
        <text class="section-title">异常指标</text>
        
        <view class="abnormal-list">
          <view 
            v-for="item in abnormalItems" 
            :key="item.id"
            class="abnormal-item"
          >
            <view class="abnormal-header">
              <text class="abnormal-name">{{ item.name }}</text>
              <view :class="['abnormal-level', item.level]">
                <text class="level-text">{{ item.levelText }}</text>
              </view>
            </view>
            <view class="abnormal-values">
              <text class="current-value">当前值: {{ item.currentValue }}</text>
              <text class="reference-range">参考范围: {{ item.referenceRange }}</text>
            </view>
            <text class="abnormal-suggestion">{{ item.suggestion }}</text>
          </view>
        </view>
      </view>

      <!-- 健康建议 -->
      <view class="suggestions-section">
        <text class="section-title">健康建议</text>
        
        <view class="suggestions-list">
          <view 
            v-for="(suggestion, index) in healthSuggestions" 
            :key="index"
            class="suggestion-item"
          >
            <view class="suggestion-icon">
              <text :class="['iconfont', suggestion.icon, suggestion.color]"></text>
            </view>
            <view class="suggestion-content">
              <text class="suggestion-title">{{ suggestion.title }}</text>
              <text class="suggestion-desc">{{ suggestion.description }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// #ifdef APP-PLUS || H5
import uCharts from '@/utils/u-charts.js'
// #endif

export default {
  name: 'AnalysisPage',
  data() {
    return {
      selectedTimeRange: '最近3个月',
      activeChart: 'bloodPressure',
      chartTabs: [
        { key: 'bloodPressure', label: '血压' },
        { key: 'bloodSugar', label: '血糖' },
        { key: 'cholesterol', label: '胆固醇' },
        { key: 'weight', label: '体重' }
      ],
      abnormalItems: [
        {
          id: 1,
          name: '总胆固醇',
          currentValue: '6.2 mmol/L',
          referenceRange: '3.1-5.7 mmol/L',
          level: 'high',
          levelText: '偏高',
          suggestion: '建议控制饮食，减少高脂食物摄入'
        },
        {
          id: 2,
          name: '血压',
          currentValue: '145/95 mmHg',
          referenceRange: '<140/90 mmHg',
          level: 'high',
          levelText: '偏高',
          suggestion: '建议定期监测，必要时就医'
        },
        {
          id: 3,
          name: '体重指数',
          currentValue: '26.8',
          referenceRange: '18.5-24.9',
          level: 'medium',
          levelText: '超重',
          suggestion: '建议适当运动，控制体重'
        }
      ],
      healthSuggestions: [
        {
          icon: 'icon-heart',
          color: 'text-red',
          title: '心血管健康',
          description: '定期监测血压，保持适量运动'
        },
        {
          icon: 'icon-apple',
          color: 'text-green',
          title: '饮食建议',
          description: '减少高脂高糖食物，多吃蔬菜水果'
        },
        {
          icon: 'icon-moon',
          color: 'text-blue',
          title: '作息调整',
          description: '保证充足睡眠，规律作息时间'
        }
      ]
    }
  },
  onLoad() {
    this.loadAnalysisData()
    this.initChart()
  },
  methods: {
    loadAnalysisData() {
      // 加载分析数据
      console.log('加载分析数据')
    },
    
    initChart() {
      // 初始化图表
      this.$nextTick(() => {
        this.renderChart()
      })
    },
    
    renderChart() {
      // #ifdef APP-PLUS || H5
      const ctx = uni.createCanvasContext('trendChart', this)
      // 使用uCharts渲染图表
      console.log('渲染趋势图表')
      // #endif
    },
    
    switchChart(chartType) {
      this.activeChart = chartType
      this.renderChart()
    },
    
    showTimeSelector() {
      uni.showActionSheet({
        itemList: ['最近1个月', '最近3个月', '最近6个月', '最近1年'],
        success: (res) => {
          const ranges = ['最近1个月', '最近3个月', '最近6个月', '最近1年']
          this.selectedTimeRange = ranges[res.tapIndex]
          this.loadAnalysisData()
        }
      })
    },
    
    chartTouchStart(e) {
      // 图表触摸开始
    },
    
    chartTouchMove(e) {
      // 图表触摸移动
    },
    
    chartTouchEnd(e) {
      // 图表触摸结束
    }
  }
}
</script>

<style lang="scss" scoped>
.analysis-page {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.container {
  padding: 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
  
  .title {
    font-size: 48rpx;
    font-weight: 500;
    color: var(--text-color);
  }
  
  .time-selector {
    display: flex;
    align-items: center;
    padding: 16rpx 24rpx;
    background-color: var(--card-background);
    border-radius: 12rpx;
    border: 2rpx solid var(--border-color);
    
    .time-text {
      font-size: 28rpx;
      color: var(--text-color);
      margin-right: 8rpx;
    }
    
    .iconfont {
      font-size: 24rpx;
      color: var(--text-secondary);
    }
  }
}

.overview-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.overview-card {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  
  .overview-title {
    display: block;
    font-size: 28rpx;
    color: var(--text-secondary);
    margin-bottom: 16rpx;
  }
  
  .overview-value {
    display: block;
    font-size: 40rpx;
    font-weight: 600;
    margin-bottom: 8rpx;
    
    &.good { color: var(--success-color); }
    &.warning { color: var(--warning-color); }
    &.error { color: var(--error-color); }
  }
  
  .overview-desc {
    display: block;
    font-size: 24rpx;
    color: var(--text-secondary);
  }
}

.chart-section {
  margin-bottom: 48rpx;
  
  .section-title {
    display: block;
    font-size: 36rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 24rpx;
  }
}

.chart-tabs {
  display: flex;
  background-color: var(--card-background);
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 24rpx;
}

.chart-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  
  &.active {
    background-color: var(--primary-color);
    
    .tab-text {
      color: #ffffff;
    }
  }
  
  .tab-text {
    font-size: 28rpx;
    color: var(--text-secondary);
  }
}

.chart-container {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
}

.chart-canvas {
  width: 100%;
  height: 400rpx;
}

.abnormal-section, .suggestions-section {
  margin-bottom: 48rpx;
  
  .section-title {
    display: block;
    font-size: 36rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 24rpx;
  }
}

.abnormal-item {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  
  .abnormal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
    
    .abnormal-name {
      font-size: 32rpx;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .abnormal-level {
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      
      &.high {
        background-color: rgba(255, 59, 48, 0.1);
        color: #FF3B30;
      }
      
      &.medium {
        background-color: rgba(255, 149, 0, 0.1);
        color: #FF9500;
      }
    }
  }
  
  .abnormal-values {
    margin-bottom: 16rpx;
    
    .current-value, .reference-range {
      display: block;
      font-size: 28rpx;
      color: var(--text-secondary);
      margin-bottom: 4rpx;
    }
  }
  
  .abnormal-suggestion {
    font-size: 28rpx;
    color: var(--text-color);
    line-height: 1.5;
  }
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  
  .suggestion-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    background-color: rgba(0, 122, 255, 0.1);
    
    .iconfont {
      font-size: 40rpx;
    }
  }
  
  .suggestion-content {
    flex: 1;
    
    .suggestion-title {
      display: block;
      font-size: 32rpx;
      font-weight: 500;
      color: var(--text-color);
      margin-bottom: 8rpx;
    }
    
    .suggestion-desc {
      display: block;
      font-size: 28rpx;
      color: var(--text-secondary);
      line-height: 1.5;
    }
  }
}
</style>
