<template>
  <view class="login-page">
    <view class="container">
      <!-- 头部Logo区域 -->
      <view class="header">
        <view class="logo">🏥</view>
        <text class="app-name">健康报告助手</text>
        <text class="slogan">让健康管理更简单</text>
      </view>
      
      <!-- 登录表单 -->
      <view class="login-form">
        <view class="form-item">
          <view class="input-wrapper">
            <text class="input-icon">📱</text>
            <input 
              v-model="loginForm.phone"
              type="number"
              placeholder="请输入手机号"
              class="input-field"
              maxlength="11"
            />
          </view>
        </view>
        
        <view class="form-item">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input 
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              class="input-field"
            />
            <text 
              @click="togglePassword" 
              class="toggle-password"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </text>
          </view>
        </view>
        
        <view class="form-actions">
          <button 
            @click="handleLogin"
            :disabled="loginLoading"
            class="login-btn"
            :class="{ loading: loginLoading }"
          >
            {{ loginLoading ? '登录中...' : '登录' }}
          </button>
          
          <view class="links">
            <text @click="goToRegister" class="link">注册账号</text>
            <text @click="forgotPassword" class="link">忘记密码</text>
          </view>
        </view>
      </view>
      
      <!-- 快速登录 -->
      <view class="quick-login">
        <text class="quick-title">快速体验</text>
        <button @click="quickLogin" class="quick-btn">
          🚀 游客登录
        </button>
      </view>
      
      <!-- 测试账号提示 -->
      <view class="test-info">
        <text class="test-title">测试账号</text>
        <text class="test-account">手机号: ***********</text>
        <text class="test-account">密码: 123456</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'

export default {
  name: 'LoginPage',
  setup() {
    const userStore = useUserStore()
    const appStore = useAppStore()
    
    // 响应式数据
    const showPassword = ref(false)
    const loginLoading = ref(false)
    
    const loginForm = reactive({
      phone: '',
      password: ''
    })
    
    // 切换密码显示
    const togglePassword = () => {
      showPassword.value = !showPassword.value
    }
    
    // 处理登录
    const handleLogin = async () => {
      // 表单验证
      if (!loginForm.phone) {
        appStore.showToast('请输入手机号', 'error')
        return
      }
      
      if (!/^1[3-9]\d{9}$/.test(loginForm.phone)) {
        appStore.showToast('请输入正确的手机号', 'error')
        return
      }
      
      if (!loginForm.password) {
        appStore.showToast('请输入密码', 'error')
        return
      }
      
      if (loginForm.password.length < 6) {
        appStore.showToast('密码至少6位', 'error')
        return
      }
      
      // 执行登录
      loginLoading.value = true
      
      try {
        const result = await userStore.login({
          phone: loginForm.phone,
          password: loginForm.password
        })
        
        if (result.success) {
          appStore.showToast('登录成功', 'success')
          
          // 跳转到主页
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/home/<USER>'
            })
          }, 1000)
        } else {
          appStore.showToast(result.message || '登录失败', 'error')
        }
      } catch (error) {
        console.error('登录错误:', error)
        appStore.showToast('登录失败，请重试', 'error')
      } finally {
        loginLoading.value = false
      }
    }
    
    // 快速登录（游客模式）
    const quickLogin = async () => {
      try {
        const result = await userStore.login({
          phone: '***********',
          password: '123456'
        })
        
        if (result.success) {
          appStore.showToast('游客登录成功', 'success')
          
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/home/<USER>'
            })
          }, 1000)
        }
      } catch (error) {
        console.error('快速登录失败:', error)
        appStore.showToast('快速登录失败', 'error')
      }
    }
    
    // 跳转到注册页
    const goToRegister = () => {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    }
    
    // 忘记密码
    const forgotPassword = () => {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
    
    return {
      loginForm,
      showPassword,
      loginLoading,
      togglePassword,
      handleLogin,
      quickLogin,
      goToRegister,
      forgotPassword
    }
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.container {
  padding: 60rpx 40rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
  
  .logo {
    font-size: 120rpx;
    margin-bottom: 30rpx;
  }
  
  .app-name {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
  }
  
  .slogan {
    display: block;
    font-size: 28rpx;
    opacity: 0.9;
  }
}

.login-form {
  flex: 1;
  
  .form-item {
    margin-bottom: 30rpx;
  }
  
  .input-wrapper {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16rpx;
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10rpx);
    
    .input-icon {
      font-size: 32rpx;
      margin-right: 20rpx;
    }
    
    .input-field {
      flex: 1;
      height: 100rpx;
      font-size: 30rpx;
      color: white;
      background: transparent;
      border: none;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }
    }
    
    .toggle-password {
      font-size: 32rpx;
      padding: 10rpx;
    }
  }
  
  .form-actions {
    margin-top: 60rpx;
    
    .login-btn {
      width: 100%;
      height: 100rpx;
      background: white;
      color: #667eea;
      border: none;
      border-radius: 16rpx;
      font-size: 32rpx;
      font-weight: 600;
      margin-bottom: 40rpx;
      
      &.loading {
        opacity: 0.7;
      }
      
      &:active {
        opacity: 0.8;
      }
    }
    
    .links {
      display: flex;
      justify-content: space-between;
      
      .link {
        font-size: 28rpx;
        opacity: 0.9;
        text-decoration: underline;
      }
    }
  }
}

.quick-login {
  text-align: center;
  margin-bottom: 40rpx;
  
  .quick-title {
    display: block;
    font-size: 24rpx;
    opacity: 0.8;
    margin-bottom: 20rpx;
  }
  
  .quick-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 12rpx;
    padding: 20rpx 40rpx;
    font-size: 26rpx;
  }
}

.test-info {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  
  .test-title {
    display: block;
    font-size: 24rpx;
    opacity: 0.8;
    margin-bottom: 10rpx;
  }
  
  .test-account {
    display: block;
    font-size: 22rpx;
    opacity: 0.7;
    margin-bottom: 5rpx;
  }
}
</style>
