# 项目设置指南

## 🚨 Vue3兼容性修复

如果您遇到"根目录缺少 index.html"或Vue3兼容性问题，请按以下步骤操作：

### 1. 确认HBuilderX版本
确保您使用的是 **HBuilderX 3.8.0** 或更高版本，这是支持Vue3的最低版本。

### 2. 创建新的uni-app项目（推荐方法）

如果问题持续存在，建议重新创建项目：

1. 打开HBuilderX
2. 文件 → 新建 → 项目
3. 选择 "uni-app" 项目
4. 选择 "Vue3/Vite版" 模板
5. 输入项目名称：健康报告助手
6. 创建项目后，将我们的代码文件复制到新项目中

### 3. 手动配置Vue3（当前项目修复）

如果您想在当前项目中修复，请确保以下文件存在且配置正确：

#### ✅ 已创建的关键文件：
- `index.html` - H5入口文件
- `vue.config.js` - Vue配置
- `babel.config.js` - Babel配置
- `postcss.config.js` - PostCSS配置
- `.env` - 环境变量

#### 📝 检查manifest.json配置：
确保 `vueVersion` 设置为 "3"：
```json
{
  "vueVersion": "3",
  "app-plus": {
    "compatible": {
      "ignoreVersion": true
    }
  }
}
```

#### 📝 检查pages.json配置：
确保包含 `vueVersion` 配置：
```json
{
  "vueVersion": "3",
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
  }
}
```

### 4. 安装依赖

在项目根目录运行：
```bash
npm install
```

或者双击运行 `start.bat` 脚本选择"安装依赖"。

### 5. 运行项目

#### 方法1: 使用npm命令
```bash
# H5开发
npm run dev:h5

# 微信小程序开发  
npm run dev:mp-weixin

# App开发
npm run dev:app-plus
```

#### 方法2: 使用HBuilderX
1. 在HBuilderX中打开项目
2. 点击工具栏的"运行" → "运行到浏览器" → "Chrome"
3. 或选择其他运行目标

#### 方法3: 使用启动脚本
双击 `start.bat` 文件，选择对应的运行模式。

### 6. 测试Vue3功能

访问测试页面验证Vue3功能是否正常：
- 在浏览器中访问: `http://localhost:8080/#/pages/test/test`
- 或在HBuilderX中运行到测试页面

## 🔧 常见问题解决

### Q1: 提示"Vue版本不匹配"
**解决方案**: 
1. 删除 `node_modules` 文件夹
2. 删除 `package-lock.json` 文件
3. 重新运行 `npm install`

### Q2: 编译错误"Cannot resolve module"
**解决方案**:
1. 检查 `vue.config.js` 中的路径别名配置
2. 确保所有导入路径正确
3. 重启开发服务器

### Q3: 样式不生效
**解决方案**:
1. 确保 `uni.scss` 文件存在
2. 检查 `vue.config.js` 中的SCSS配置
3. 重新编译项目

### Q4: 组件无法识别
**解决方案**:
1. 检查 `pages.json` 中的 `easycom` 配置
2. 确保组件路径正确
3. 重启HBuilderX

## 📱 平台特定配置

### H5平台
- 确保 `index.html` 文件存在
- 配置 `vue.config.js` 中的devServer

### 微信小程序
- 需要微信开发者工具
- 配置小程序AppID

### App平台
- 需要Android Studio或Xcode
- 配置原生插件权限

## 🎯 下一步操作

1. **验证项目运行**: 先运行测试页面确认Vue3配置正确
2. **配置OCR服务**: 在 `services/ocr.js` 中配置真实的API密钥
3. **自定义样式**: 修改 `styles/global.scss` 中的主题变量
4. **添加图标**: 在 `static/` 目录中添加应用图标和tabbar图标
5. **测试功能**: 逐一测试各个页面和功能模块

## 📞 技术支持

如果仍然遇到问题，请提供：
1. HBuilderX版本号
2. 具体错误信息
3. 运行环境（Windows/Mac）
4. 项目运行命令

我将为您提供针对性的解决方案。
