<template>
  <view class="page">
    <view class="container">
      <text class="title">🏥 健康报告助手</text>
      <text class="subtitle">Vue3 测试页面</text>

      <view class="test-section">
        <text class="count">计数: {{ count }}</text>
        <button @click="increment" class="btn">点击 +1</button>
      </view>

      <view class="nav-section">
        <button @click="showMessage" class="btn secondary">显示消息</button>
        <button @click="testStorage" class="btn secondary">测试存储</button>
      </view>

      <text class="status">{{ status }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SimplePage',
  data() {
    return {
      count: 0,
      status: 'Vue3 已加载'
    }
  },
  onLoad() {
    console.log('Simple page loaded')
    this.status = 'Vue3 页面加载成功 ✅'
  },
  methods: {
    increment() {
      this.count++
      console.log('Count:', this.count)
      this.status = `计数器工作正常，当前值: ${this.count}`
    },

    showMessage() {
      uni.showToast({
        title: 'Hello Vue3!',
        icon: 'success'
      })
      this.status = 'uni API 工作正常 ✅'
    },

    testStorage() {
      try {
        uni.setStorageSync('test', 'Vue3 Storage Test')
        const value = uni.getStorageSync('test')
        this.status = `存储测试成功: ${value}`

        uni.showToast({
          title: '存储测试成功',
          icon: 'success'
        })
      } catch (error) {
        this.status = '存储测试失败: ' + error.message
        console.error('Storage test failed:', error)
      }
    }
  }
}
</script>

<style>
.page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
  padding: 60rpx 40rpx;
}

.container {
  text-align: center;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 60rpx;
}

.test-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.count {
  display: block;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.btn {
  background: white;
  color: #667eea;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  margin: 10rpx;
}

.btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.nav-section {
  margin-bottom: 40rpx;
}

.status {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
  margin-top: 40rpx;
}
</style>
