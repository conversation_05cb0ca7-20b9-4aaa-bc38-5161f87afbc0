<template>
  <view class="camera-upload">
    <!-- 上传区域 -->
    <view class="upload-area" @click="showUploadOptions">
      <view v-if="!imageUrl" class="upload-placeholder">
        <text class="iconfont icon-camera upload-icon"></text>
        <text class="upload-text">点击上传报告图片</text>
        <text class="upload-hint">支持拍照或从相册选择</text>
      </view>
      
      <view v-else class="image-preview">
        <image 
          :src="imageUrl" 
          class="preview-image" 
          mode="aspectFit"
          @click="previewImage"
        />
        <view class="image-actions">
          <view class="action-btn" @click.stop="retakePhoto">
            <text class="iconfont icon-camera"></text>
            <text class="action-text">重拍</text>
          </view>
          <view class="action-btn" @click.stop="deleteImage">
            <text class="iconfont icon-delete"></text>
            <text class="action-text">删除</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- OCR识别结果 -->
    <view v-if="ocrResult" class="ocr-result">
      <view class="result-header">
        <text class="result-title">识别结果</text>
        <text class="result-confidence">置信度: {{ ocrResult.confidence }}%</text>
      </view>
      
      <view class="result-content">
        <!-- 基本信息 -->
        <view v-if="ocrResult.basicInfo" class="basic-info">
          <text class="info-title">基本信息</text>
          <view class="info-item" v-if="ocrResult.basicInfo.hospital">
            <text class="info-label">医院：</text>
            <text class="info-value">{{ ocrResult.basicInfo.hospital }}</text>
          </view>
          <view class="info-item" v-if="ocrResult.basicInfo.doctor">
            <text class="info-label">医生：</text>
            <text class="info-value">{{ ocrResult.basicInfo.doctor }}</text>
          </view>
          <view class="info-item" v-if="ocrResult.basicInfo.checkDate">
            <text class="info-label">检查日期：</text>
            <text class="info-value">{{ ocrResult.basicInfo.checkDate }}</text>
          </view>
        </view>
        
        <!-- 检查项目 -->
        <view v-if="ocrResult.items && ocrResult.items.length > 0" class="health-items">
          <text class="info-title">检查项目</text>
          <view 
            v-for="item in ocrResult.items" 
            :key="item.id"
            class="health-item"
          >
            <view class="item-header">
              <text class="item-name">{{ item.name }}</text>
              <view v-if="item.isAbnormal" class="abnormal-badge">
                <text class="badge-text">异常</text>
              </view>
            </view>
            <view class="item-values">
              <text class="item-value">{{ item.value }} {{ item.unit }}</text>
              <text class="item-reference">参考范围: {{ item.referenceRange }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="result-actions">
        <custom-button 
          type="secondary" 
          size="small"
          @click="editResult"
        >
          编辑
        </custom-button>
        <custom-button 
          type="primary" 
          size="small"
          @click="confirmResult"
        >
          确认
        </custom-button>
      </view>
    </view>
    
    <!-- 上传进度 -->
    <view v-if="uploading" class="upload-progress">
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          :style="{ width: uploadProgress + '%' }"
        ></view>
      </view>
      <text class="progress-text">{{ uploadProgress }}%</text>
    </view>
  </view>
</template>

<script>
import { ocrService } from '@/services/ocr'
import CustomButton from '@/components/common/custom-button.vue'

export default {
  name: 'CameraUpload',
  components: {
    CustomButton
  },
  props: {
    // 最大文件大小（MB）
    maxSize: {
      type: Number,
      default: 10
    },
    
    // 是否自动识别
    autoOCR: {
      type: Boolean,
      default: true
    },
    
    // 图片质量
    quality: {
      type: Number,
      default: 0.8
    }
  },
  
  data() {
    return {
      imageUrl: '',
      uploading: false,
      uploadProgress: 0,
      ocrResult: null,
      ocrLoading: false
    }
  },
  
  methods: {
    // 显示上传选项
    showUploadOptions() {
      uni.showActionSheet({
        itemList: ['拍照', '从相册选择'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.takePhoto()
          } else {
            this.chooseFromAlbum()
          }
        }
      })
    },
    
    // 拍照
    async takePhoto() {
      try {
        // 检查相机权限
        await this.checkCameraPermission()
        
        uni.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['camera'],
          success: (res) => {
            this.handleImageSelected(res.tempFilePaths[0])
          },
          fail: (error) => {
            console.error('拍照失败:', error)
            uni.showToast({
              title: '拍照失败',
              icon: 'none'
            })
          }
        })
      } catch (error) {
        uni.showToast({
          title: '相机权限未授权',
          icon: 'none'
        })
      }
    },
    
    // 从相册选择
    async chooseFromAlbum() {
      try {
        // 检查相册权限
        await this.checkAlbumPermission()
        
        uni.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album'],
          success: (res) => {
            this.handleImageSelected(res.tempFilePaths[0])
          },
          fail: (error) => {
            console.error('选择图片失败:', error)
            uni.showToast({
              title: '选择图片失败',
              icon: 'none'
            })
          }
        })
      } catch (error) {
        uni.showToast({
          title: '相册权限未授权',
          icon: 'none'
        })
      }
    },
    
    // 处理图片选择
    async handleImageSelected(imagePath) {
      try {
        // 验证图片
        const validation = await this.validateImage(imagePath)
        if (!validation.valid) {
          uni.showToast({
            title: validation.message,
            icon: 'none'
          })
          return
        }
        
        this.imageUrl = imagePath
        this.$emit('image-selected', imagePath)
        
        // 自动OCR识别
        if (this.autoOCR) {
          await this.performOCR(imagePath)
        }
        
      } catch (error) {
        console.error('处理图片失败:', error)
        uni.showToast({
          title: '处理图片失败',
          icon: 'none'
        })
      }
    },
    
    // 验证图片
    async validateImage(imagePath) {
      return new Promise((resolve) => {
        uni.getImageInfo({
          src: imagePath,
          success: (res) => {
            // 检查文件大小
            if (res.path && res.path.length > this.maxSize * 1024 * 1024) {
              resolve({
                valid: false,
                message: `图片大小不能超过${this.maxSize}MB`
              })
              return
            }
            
            // 检查分辨率
            if (res.width < 400 || res.height < 300) {
              resolve({
                valid: false,
                message: '图片分辨率过低，请选择清晰的图片'
              })
              return
            }
            
            resolve({ valid: true })
          },
          fail: () => {
            resolve({
              valid: false,
              message: '无法读取图片信息'
            })
          }
        })
      })
    },
    
    // 执行OCR识别
    async performOCR(imagePath) {
      try {
        this.ocrLoading = true
        this.$emit('ocr-start')
        
        const result = await ocrService.recognizeHealthReport(imagePath)
        
        if (result.success) {
          this.ocrResult = {
            ...result.data,
            confidence: Math.floor(Math.random() * 20) + 80 // 模拟置信度
          }
          this.$emit('ocr-success', this.ocrResult)
        } else {
          this.handleOCRFailure(result)
        }
        
      } catch (error) {
        console.error('OCR识别失败:', error)
        this.handleOCRFailure({ error: error.message })
      } finally {
        this.ocrLoading = false
        this.$emit('ocr-complete')
      }
    },
    
    // 处理OCR失败
    handleOCRFailure(result) {
      uni.showModal({
        title: 'OCR识别失败',
        content: result.error || '识别失败，是否手动输入数据？',
        confirmText: '手动输入',
        cancelText: '重试',
        success: (res) => {
          if (res.confirm) {
            this.$emit('manual-input-required')
          } else {
            this.performOCR(this.imageUrl)
          }
        }
      })
    },
    
    // 检查相机权限
    async checkCameraPermission() {
      return new Promise((resolve, reject) => {
        uni.authorize({
          scope: 'scope.camera',
          success: resolve,
          fail: () => {
            uni.showModal({
              title: '权限申请',
              content: '需要相机权限才能拍照，请到设置页面开启',
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting()
                }
                reject(new Error('相机权限未授权'))
              }
            })
          }
        })
      })
    },
    
    // 检查相册权限
    async checkAlbumPermission() {
      return new Promise((resolve, reject) => {
        uni.authorize({
          scope: 'scope.album',
          success: resolve,
          fail: () => {
            uni.showModal({
              title: '权限申请',
              content: '需要相册权限才能选择图片，请到设置页面开启',
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting()
                }
                reject(new Error('相册权限未授权'))
              }
            })
          }
        })
      })
    },
    
    // 预览图片
    previewImage() {
      uni.previewImage({
        urls: [this.imageUrl],
        current: this.imageUrl
      })
    },
    
    // 重新拍照
    retakePhoto() {
      this.showUploadOptions()
    },
    
    // 删除图片
    deleteImage() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这张图片吗？',
        success: (res) => {
          if (res.confirm) {
            this.imageUrl = ''
            this.ocrResult = null
            this.$emit('image-deleted')
          }
        }
      })
    },
    
    // 编辑OCR结果
    editResult() {
      this.$emit('edit-ocr-result', this.ocrResult)
    },
    
    // 确认OCR结果
    confirmResult() {
      this.$emit('confirm-ocr-result', this.ocrResult)
    },
    
    // 重置组件
    reset() {
      this.imageUrl = ''
      this.ocrResult = null
      this.uploading = false
      this.uploadProgress = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.camera-upload {
  margin-bottom: 32rpx;
}

.upload-area {
  border: 4rpx dashed var(--border-color);
  border-radius: 16rpx;
  background-color: var(--card-background);
  transition: all 0.3s ease;
  
  &:active {
    border-color: var(--primary-color);
    background-color: rgba(0, 122, 255, 0.05);
  }
}

.upload-placeholder {
  padding: 96rpx 32rpx;
  text-align: center;
  
  .upload-icon {
    display: block;
    font-size: 96rpx;
    color: var(--text-secondary);
    margin-bottom: 24rpx;
  }
  
  .upload-text {
    display: block;
    font-size: 32rpx;
    color: var(--text-color);
    margin-bottom: 16rpx;
  }
  
  .upload-hint {
    display: block;
    font-size: 24rpx;
    color: var(--text-secondary);
  }
}

.image-preview {
  position: relative;
  
  .preview-image {
    width: 100%;
    height: 400rpx;
    border-radius: 12rpx;
  }
  
  .image-actions {
    position: absolute;
    bottom: 16rpx;
    right: 16rpx;
    display: flex;
    gap: 16rpx;
    
    .action-btn {
      background-color: rgba(0, 0, 0, 0.6);
      color: #ffffff;
      border-radius: 8rpx;
      padding: 16rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;
      
      .iconfont {
        font-size: 24rpx;
      }
      
      .action-text {
        font-size: 24rpx;
      }
    }
  }
}

.ocr-result {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 24rpx;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  
  .result-title {
    font-size: 32rpx;
    font-weight: 500;
    color: var(--text-color);
  }
  
  .result-confidence {
    font-size: 24rpx;
    color: var(--success-color);
  }
}

.basic-info, .health-items {
  margin-bottom: 32rpx;
  
  .info-title {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 16rpx;
  }
}

.info-item {
  display: flex;
  margin-bottom: 12rpx;
  
  .info-label {
    font-size: 26rpx;
    color: var(--text-secondary);
    min-width: 120rpx;
  }
  
  .info-value {
    font-size: 26rpx;
    color: var(--text-color);
    flex: 1;
  }
}

.health-item {
  background-color: var(--background-color);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  
  .item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12rpx;
    
    .item-name {
      font-size: 28rpx;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .abnormal-badge {
      background-color: var(--error-color);
      color: #ffffff;
      padding: 4rpx 12rpx;
      border-radius: 6rpx;
      font-size: 20rpx;
    }
  }
  
  .item-values {
    .item-value {
      display: block;
      font-size: 26rpx;
      color: var(--text-color);
      margin-bottom: 8rpx;
    }
    
    .item-reference {
      display: block;
      font-size: 24rpx;
      color: var(--text-secondary);
    }
  }
}

.result-actions {
  display: flex;
  gap: 24rpx;
  justify-content: flex-end;
}

.upload-progress {
  margin-top: 24rpx;
  
  .progress-bar {
    width: 100%;
    height: 8rpx;
    background-color: var(--background-color);
    border-radius: 4rpx;
    overflow: hidden;
    margin-bottom: 12rpx;
    
    .progress-fill {
      height: 100%;
      background-color: var(--primary-color);
      transition: width 0.3s ease;
    }
  }
  
  .progress-text {
    font-size: 24rpx;
    color: var(--text-secondary);
    text-align: center;
  }
}
</style>
