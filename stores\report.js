import { defineStore } from 'pinia'
import { generateId } from '@/utils/common'

export const useReportStore = defineStore('report', {
  state: () => ({
    reports: [],
    currentReport: null,
    loading: false,
    filters: {
      timeRange: 'all', // all, recent, month, year
      category: 'all',   // all, blood, urine, imaging, etc.
      hospital: 'all'
    },
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    }
  }),

  getters: {
    // 过滤后的报告列表
    filteredReports: (state) => {
      let filtered = [...state.reports]
      
      // 时间范围过滤
      if (state.filters.timeRange !== 'all') {
        const now = new Date()
        const filterDate = new Date()
        
        switch (state.filters.timeRange) {
          case 'recent':
            filterDate.setDate(now.getDate() - 30)
            break
          case 'month':
            filterDate.setMonth(now.getMonth() - 1)
            break
          case 'year':
            filterDate.setFullYear(now.getFullYear() - 1)
            break
        }
        
        filtered = filtered.filter(report => 
          new Date(report.checkDate) >= filterDate
        )
      }
      
      // 分类过滤
      if (state.filters.category !== 'all') {
        filtered = filtered.filter(report =>
          report.items.some(item => item.category === state.filters.category)
        )
      }
      
      // 医院过滤
      if (state.filters.hospital !== 'all') {
        filtered = filtered.filter(report => 
          report.hospital === state.filters.hospital
        )
      }
      
      // 按日期倒序排列
      return filtered.sort((a, b) => 
        new Date(b.checkDate) - new Date(a.checkDate)
      )
    },

    // 异常报告数量
    abnormalReportsCount: (state) => {
      return state.reports.filter(report =>
        report.items.some(item => item.isAbnormal)
      ).length
    },

    // 本月新增报告数量
    monthlyReportsCount: (state) => {
      const now = new Date()
      const currentMonth = now.getMonth()
      const currentYear = now.getFullYear()
      
      return state.reports.filter(report => {
        const reportDate = new Date(report.createdAt)
        return reportDate.getMonth() === currentMonth && 
               reportDate.getFullYear() === currentYear
      }).length
    },

    // 待处理报告数量
    pendingReportsCount: (state) => {
      return state.reports.filter(report => 
        report.syncStatus === 'pending' || 
        report.items.some(item => item.isAbnormal && !item.reviewed)
      ).length
    }
  },

  actions: {
    // 加载报告列表
    async loadReports(refresh = false) {
      if (this.loading && !refresh) return
      
      this.loading = true
      
      try {
        // 模拟API调用
        const response = await this.mockGetReports()
        
        if (refresh) {
          this.reports = response.data
        } else {
          this.reports = [...this.reports, ...response.data]
        }
        
        this.pagination.total = response.total
        this.pagination.current = response.current
        
      } catch (error) {
        console.error('加载报告失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 获取报告详情
    async getReportDetail(reportId) {
      try {
        const report = this.reports.find(r => r.id === reportId)
        if (report) {
          this.currentReport = report
          return report
        }
        
        // 从服务器获取
        const response = await this.mockGetReportDetail(reportId)
        this.currentReport = response.data
        return response.data
        
      } catch (error) {
        console.error('获取报告详情失败:', error)
        throw error
      }
    },

    // 添加新报告
    async addReport(reportData) {
      try {
        uni.showLoading({ title: '保存中...' })
        
        const newReport = {
          id: generateId(),
          userId: this.userInfo?.id,
          ...reportData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          syncStatus: 'local'
        }
        
        // 保存到本地
        this.reports.unshift(newReport)
        await this.saveToLocal()
        
        // 尝试同步到服务器
        try {
          await this.syncReport(newReport)
        } catch (syncError) {
          console.warn('同步失败，将在后台重试:', syncError)
        }
        
        uni.hideLoading()
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        return newReport
        
      } catch (error) {
        uni.hideLoading()
        console.error('添加报告失败:', error)
        throw error
      }
    },

    // 更新报告
    async updateReport(reportId, updateData) {
      try {
        uni.showLoading({ title: '更新中...' })
        
        const index = this.reports.findIndex(r => r.id === reportId)
        if (index === -1) {
          throw new Error('报告不存在')
        }
        
        const updatedReport = {
          ...this.reports[index],
          ...updateData,
          updatedAt: new Date().toISOString(),
          syncStatus: 'pending'
        }
        
        this.reports[index] = updatedReport
        
        if (this.currentReport?.id === reportId) {
          this.currentReport = updatedReport
        }
        
        await this.saveToLocal()
        
        // 尝试同步到服务器
        try {
          await this.syncReport(updatedReport)
        } catch (syncError) {
          console.warn('同步失败，将在后台重试:', syncError)
        }
        
        uni.hideLoading()
        uni.showToast({
          title: '更新成功',
          icon: 'success'
        })
        
        return updatedReport
        
      } catch (error) {
        uni.hideLoading()
        console.error('更新报告失败:', error)
        throw error
      }
    },

    // 删除报告
    async deleteReport(reportId) {
      try {
        const index = this.reports.findIndex(r => r.id === reportId)
        if (index === -1) {
          throw new Error('报告不存在')
        }
        
        // 从列表中移除
        this.reports.splice(index, 1)
        
        // 清除当前报告
        if (this.currentReport?.id === reportId) {
          this.currentReport = null
        }
        
        await this.saveToLocal()
        
        // 同步删除到服务器
        try {
          await this.mockDeleteReport(reportId)
        } catch (syncError) {
          console.warn('同步删除失败:', syncError)
        }
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
        
      } catch (error) {
        console.error('删除报告失败:', error)
        throw error
      }
    },

    // 导出报告数据
    async exportReports(format = 'json') {
      try {
        uni.showLoading({ title: '导出中...' })
        
        const exportData = {
          exportTime: new Date().toISOString(),
          totalCount: this.reports.length,
          reports: this.reports
        }
        
        if (format === 'json') {
          const jsonStr = JSON.stringify(exportData, null, 2)
          
          // #ifdef APP-PLUS
          // 保存到文件
          const filePath = await this.saveJsonFile(jsonStr)
          uni.hideLoading()
          
          uni.showModal({
            title: '导出成功',
            content: `文件已保存到: ${filePath}`,
            showCancel: false
          })
          // #endif
          
          // #ifdef H5
          // 下载文件
          this.downloadFile(jsonStr, 'health-reports.json', 'application/json')
          uni.hideLoading()
          // #endif
          
        } else if (format === 'pdf') {
          // PDF导出功能（待实现）
          throw new Error('PDF导出功能开发中')
        }
        
      } catch (error) {
        uni.hideLoading()
        console.error('导出失败:', error)
        throw error
      }
    },

    // 设置过滤条件
    setFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
    },

    // 清除过滤条件
    clearFilters() {
      this.filters = {
        timeRange: 'all',
        category: 'all',
        hospital: 'all'
      }
    },

    // 保存到本地存储
    async saveToLocal() {
      try {
        uni.setStorageSync('reports', this.reports)
      } catch (error) {
        console.error('本地保存失败:', error)
        throw new Error('本地保存失败')
      }
    },

    // 从本地存储加载
    async loadFromLocal() {
      try {
        const localReports = uni.getStorageSync('reports')
        if (localReports && Array.isArray(localReports)) {
          this.reports = localReports
        }
      } catch (error) {
        console.error('本地加载失败:', error)
      }
    },

    // 同步报告到服务器
    async syncReport(report) {
      // 模拟API调用
      await this.delay(1000)
      
      // 更新同步状态
      const index = this.reports.findIndex(r => r.id === report.id)
      if (index !== -1) {
        this.reports[index].syncStatus = 'synced'
        await this.saveToLocal()
      }
    },

    // 模拟API方法
    async mockGetReports() {
      await this.delay(1000)
      
      return {
        success: true,
        data: [
          {
            id: '1',
            title: '年度体检报告',
            hospital: '北京协和医院',
            doctor: '张医生',
            checkDate: '2024-12-15',
            reportDate: '2024-12-16',
            items: [
              {
                id: '1-1',
                name: '血红蛋白',
                value: '145',
                unit: 'g/L',
                referenceRange: '120-160',
                isAbnormal: false,
                category: '血常规'
              }
            ],
            tags: ['体检', '年度'],
            notes: '各项指标正常',
            createdAt: new Date().toISOString(),
            syncStatus: 'synced'
          }
        ],
        total: 1,
        current: 1
      }
    },

    async mockGetReportDetail(reportId) {
      await this.delay(500)
      
      return {
        success: true,
        data: this.reports.find(r => r.id === reportId)
      }
    },

    async mockDeleteReport(reportId) {
      await this.delay(500)
      return { success: true }
    },

    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
})
