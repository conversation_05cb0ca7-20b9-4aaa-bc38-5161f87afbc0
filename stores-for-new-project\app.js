import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 应用基本信息
    appInfo: {
      name: '健康报告助手',
      version: '1.0.0',
      build: '100'
    },
    
    // 系统信息
    systemInfo: null,
    
    // 应用状态
    isFirstLaunch: true,
    isLoading: false,
    networkStatus: 'unknown',
    
    // 权限状态
    permissions: {
      camera: false,
      storage: false,
      location: false
    },
    
    // 主题设置
    theme: {
      mode: 'light', // light, dark, auto
      primaryColor: '#007AFF',
      fontSize: 'medium' // small, medium, large
    }
  }),
  
  getters: {
    // 是否为暗色主题
    isDarkMode: (state) => {
      if (state.theme.mode === 'auto') {
        // 根据系统时间判断
        const hour = new Date().getHours()
        return hour < 7 || hour > 19
      }
      return state.theme.mode === 'dark'
    },
    
    // 获取状态栏高度
    statusBarHeight: (state) => {
      return state.systemInfo?.statusBarHeight || 0
    },
    
    // 获取安全区域
    safeArea: (state) => {
      return state.systemInfo?.safeArea || {}
    }
  },
  
  actions: {
    // 初始化应用
    async initApp() {
      try {
        console.log('开始初始化应用...')
        
        // 获取系统信息
        await this.getSystemInfo()
        
        // 检查首次启动
        this.checkFirstLaunch()
        
        // 检查网络状态
        this.checkNetworkStatus()
        
        // 检查权限
        await this.checkPermissions()
        
        console.log('应用初始化完成')
      } catch (error) {
        console.error('应用初始化失败:', error)
      }
    },
    
    // 获取系统信息
    async getSystemInfo() {
      try {
        const systemInfo = uni.getSystemInfoSync()
        this.systemInfo = systemInfo
        console.log('系统信息:', systemInfo)
      } catch (error) {
        console.error('获取系统信息失败:', error)
      }
    },
    
    // 检查首次启动
    checkFirstLaunch() {
      const hasLaunched = uni.getStorageSync('hasLaunched')
      this.isFirstLaunch = !hasLaunched
      
      if (this.isFirstLaunch) {
        uni.setStorageSync('hasLaunched', true)
        console.log('检测到首次启动')
      }
    },
    
    // 检查网络状态
    checkNetworkStatus() {
      uni.getNetworkType({
        success: (res) => {
          this.networkStatus = res.networkType
          console.log('网络状态:', res.networkType)
        },
        fail: (error) => {
          console.error('获取网络状态失败:', error)
          this.networkStatus = 'unknown'
        }
      })
      
      // 监听网络状态变化
      uni.onNetworkStatusChange((res) => {
        this.networkStatus = res.networkType
        console.log('网络状态变化:', res.networkType)
      })
    },
    
    // 检查权限
    async checkPermissions() {
      // #ifdef APP-PLUS
      // 检查相机权限
      uni.authorize({
        scope: 'scope.camera',
        success: () => {
          this.permissions.camera = true
          console.log('相机权限已授权')
        },
        fail: () => {
          this.permissions.camera = false
          console.log('相机权限未授权')
        }
      })
      
      // 检查存储权限
      uni.authorize({
        scope: 'scope.writePhotosAlbum',
        success: () => {
          this.permissions.storage = true
          console.log('存储权限已授权')
        },
        fail: () => {
          this.permissions.storage = false
          console.log('存储权限未授权')
        }
      })
      // #endif
      
      // #ifdef H5
      // H5环境下默认有权限
      this.permissions.camera = true
      this.permissions.storage = true
      // #endif
    },
    
    // 请求权限
    async requestPermission(type) {
      return new Promise((resolve) => {
        // #ifdef APP-PLUS
        let scope = ''
        switch (type) {
          case 'camera':
            scope = 'scope.camera'
            break
          case 'storage':
            scope = 'scope.writePhotosAlbum'
            break
          case 'location':
            scope = 'scope.userLocation'
            break
          default:
            resolve(false)
            return
        }
        
        uni.authorize({
          scope,
          success: () => {
            this.permissions[type] = true
            resolve(true)
          },
          fail: () => {
            // 权限被拒绝，引导用户到设置页面
            uni.showModal({
              title: '权限申请',
              content: `需要${type === 'camera' ? '相机' : type === 'storage' ? '存储' : '定位'}权限才能正常使用功能，请到设置中开启`,
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting()
                }
              }
            })
            resolve(false)
          }
        })
        // #endif
        
        // #ifdef H5
        resolve(true)
        // #endif
      })
    },
    
    // 设置主题
    setTheme(mode) {
      this.theme.mode = mode
      uni.setStorageSync('theme', mode)
      
      // 应用主题样式
      this.applyTheme()
    },
    
    // 应用主题样式
    applyTheme() {
      const isDark = this.isDarkMode
      
      // 设置状态栏样式
      // #ifdef APP-PLUS
      plus.navigator.setStatusBarStyle(isDark ? 'light' : 'dark')
      // #endif
      
      console.log('主题已切换:', isDark ? 'dark' : 'light')
    },
    
    // 显示加载状态
    showLoading(title = '加载中...') {
      this.isLoading = true
      uni.showLoading({ title })
    },
    
    // 隐藏加载状态
    hideLoading() {
      this.isLoading = false
      uni.hideLoading()
    },
    
    // 显示消息提示
    showToast(title, icon = 'success') {
      uni.showToast({
        title,
        icon,
        duration: 2000
      })
    },
    
    // 显示确认对话框
    showConfirm(title, content) {
      return new Promise((resolve) => {
        uni.showModal({
          title,
          content,
          success: (res) => {
            resolve(res.confirm)
          },
          fail: () => {
            resolve(false)
          }
        })
      })
    }
  }
})
