@echo off
echo ================================
echo 健康报告助手 - 文件复制脚本
echo ================================
echo.

set /p NEW_PROJECT_PATH="请输入您的新项目路径（例如：E:\健康报告助手）: "

echo.
echo 开始复制文件...
echo.

REM 创建目录
echo 1. 创建stores目录...
if not exist "%NEW_PROJECT_PATH%\stores" mkdir "%NEW_PROJECT_PATH%\stores"

echo 2. 创建pages目录...
if not exist "%NEW_PROJECT_PATH%\pages\login" mkdir "%NEW_PROJECT_PATH%\pages\login"
if not exist "%NEW_PROJECT_PATH%\pages\home" mkdir "%NEW_PROJECT_PATH%\pages\home"

REM 复制stores文件
echo 3. 复制状态管理文件...
copy "stores-for-new-project\user.js" "%NEW_PROJECT_PATH%\stores\user.js"
copy "stores-for-new-project\app.js" "%NEW_PROJECT_PATH%\stores\app.js"
copy "stores-for-new-project\index.js" "%NEW_PROJECT_PATH%\stores\index.js"

REM 复制页面文件
echo 4. 复制页面文件...
copy "pages-for-new-project\login\login.vue" "%NEW_PROJECT_PATH%\pages\login\login.vue"
copy "pages-for-new-project\home\home.vue" "%NEW_PROJECT_PATH%\pages\home\home.vue"

REM 备份并复制配置文件
echo 5. 备份并更新配置文件...
copy "%NEW_PROJECT_PATH%\pages.json" "%NEW_PROJECT_PATH%\pages.json.backup"
copy "pages-complete.json" "%NEW_PROJECT_PATH%\pages.json"

echo.
echo ================================
echo 文件复制完成！
echo ================================
echo.
echo 下一步操作：
echo 1. 在HBuilderX中修改main.js文件
echo 2. 重启项目：npm run dev:h5
echo 3. 测试登录功能
echo.
echo 测试账号：
echo 手机号：13800138000
echo 密码：123456
echo.

pause
