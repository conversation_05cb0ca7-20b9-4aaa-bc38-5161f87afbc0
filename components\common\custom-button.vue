<template>
  <button 
    :class="buttonClass"
    :disabled="disabled || loading"
    :hover-class="hoverClass"
    @click="handleClick"
  >
    <!-- 加载图标 -->
    <view v-if="loading" class="loading-icon">
      <view class="loading-spinner"></view>
    </view>
    
    <!-- 图标 -->
    <text 
      v-if="icon && !loading" 
      :class="['iconfont', icon, 'button-icon']"
    ></text>
    
    <!-- 文本内容 -->
    <text class="button-text">
      <slot>{{ text }}</slot>
    </text>
  </button>
</template>

<script>
export default {
  name: 'CustomButton',
  props: {
    // 按钮类型
    type: {
      type: String,
      default: 'primary', // primary, secondary, success, warning, error, text
      validator: (value) => {
        return ['primary', 'secondary', 'success', 'warning', 'error', 'text'].includes(value)
      }
    },
    
    // 按钮大小
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: (value) => {
        return ['small', 'medium', 'large'].includes(value)
      }
    },
    
    // 按钮形状
    shape: {
      type: String,
      default: 'rounded', // rounded, square, circle
      validator: (value) => {
        return ['rounded', 'square', 'circle'].includes(value)
      }
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 是否加载中
    loading: {
      type: Boolean,
      default: false
    },
    
    // 按钮文本
    text: {
      type: String,
      default: ''
    },
    
    // 图标
    icon: {
      type: String,
      default: ''
    },
    
    // 是否块级元素
    block: {
      type: Boolean,
      default: false
    },
    
    // 是否镂空
    plain: {
      type: Boolean,
      default: false
    }
  },
  
  computed: {
    buttonClass() {
      const classes = ['custom-button']
      
      // 类型样式
      classes.push(`button-${this.type}`)
      
      // 大小样式
      classes.push(`button-${this.size}`)
      
      // 形状样式
      classes.push(`button-${this.shape}`)
      
      // 状态样式
      if (this.disabled) classes.push('button-disabled')
      if (this.loading) classes.push('button-loading')
      if (this.block) classes.push('button-block')
      if (this.plain) classes.push('button-plain')
      
      return classes.join(' ')
    },
    
    hoverClass() {
      return this.disabled || this.loading ? '' : 'button-hover'
    }
  },
  
  methods: {
    handleClick(event) {
      if (this.disabled || this.loading) return
      
      this.$emit('click', event)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  
  // 基础样式
  &.button-primary {
    background-color: var(--primary-color);
    color: #ffffff;
    
    &.button-plain {
      background-color: transparent;
      color: var(--primary-color);
      border: 2rpx solid var(--primary-color);
    }
  }
  
  &.button-secondary {
    background-color: #f8f9fa;
    color: var(--text-color);
    
    &.button-plain {
      background-color: transparent;
      color: var(--text-color);
      border: 2rpx solid var(--border-color);
    }
  }
  
  &.button-success {
    background-color: var(--success-color);
    color: #ffffff;
    
    &.button-plain {
      background-color: transparent;
      color: var(--success-color);
      border: 2rpx solid var(--success-color);
    }
  }
  
  &.button-warning {
    background-color: var(--warning-color);
    color: #ffffff;
    
    &.button-plain {
      background-color: transparent;
      color: var(--warning-color);
      border: 2rpx solid var(--warning-color);
    }
  }
  
  &.button-error {
    background-color: var(--error-color);
    color: #ffffff;
    
    &.button-plain {
      background-color: transparent;
      color: var(--error-color);
      border: 2rpx solid var(--error-color);
    }
  }
  
  &.button-text {
    background-color: transparent;
    color: var(--primary-color);
    border: none;
  }
  
  // 大小样式
  &.button-small {
    padding: 16rpx 32rpx;
    font-size: 24rpx;
    min-height: 64rpx;
  }
  
  &.button-medium {
    padding: 24rpx 48rpx;
    font-size: 28rpx;
    min-height: 80rpx;
  }
  
  &.button-large {
    padding: 32rpx 64rpx;
    font-size: 32rpx;
    min-height: 96rpx;
  }
  
  // 形状样式
  &.button-rounded {
    border-radius: 12rpx;
  }
  
  &.button-square {
    border-radius: 0;
  }
  
  &.button-circle {
    border-radius: 50%;
    width: 80rpx;
    height: 80rpx;
    padding: 0;
    
    &.button-small {
      width: 64rpx;
      height: 64rpx;
    }
    
    &.button-large {
      width: 96rpx;
      height: 96rpx;
    }
  }
  
  // 状态样式
  &.button-disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.button-loading {
    cursor: not-allowed;
  }
  
  &.button-block {
    width: 100%;
    display: flex;
  }
  
  // 悬停效果
  &.button-hover {
    opacity: 0.8;
    transform: translateY(-2rpx);
  }
}

.loading-icon {
  margin-right: 16rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.button-icon {
  margin-right: 16rpx;
  font-size: 1.2em;
}

.button-text {
  flex: 1;
}

// 特殊样式
.custom-button.button-text {
  padding: 16rpx;
  min-height: auto;
  
  &:hover {
    background-color: rgba(0, 122, 255, 0.1);
  }
}
</style>
