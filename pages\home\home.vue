<template>
  <view class="home-page">
    <view class="container">
      <!-- 标题部分 -->
      <view class="header">
        <text class="title">健康概览</text>
        <text class="subtitle">今天是美好的一天，保持健康生活吧！</text>
      </view>

      <!-- 健康指标卡片 -->
      <view class="metrics-grid">
        <view 
          v-for="metric in healthMetrics" 
          :key="metric.title"
          class="metric-card"
        >
          <view :class="['metric-icon', metric.bgColor]">
            <text :class="['iconfont', metric.icon, metric.color]"></text>
          </view>
          <view class="metric-content">
            <text class="metric-value">{{ metric.value }}</text>
            <text class="metric-title">{{ metric.title }}</text>
            <text class="metric-unit">{{ metric.unit }}</text>
          </view>
        </view>
      </view>

      <!-- 快速功能 -->
      <view class="quick-actions">
        <text class="section-title">快速功能</text>
        
        <!-- 体检提醒 -->
        <view class="action-card">
          <view class="action-content">
            <view class="action-icon bg-blue">
              <text class="iconfont icon-calendar text-blue"></text>
            </view>
            <view class="action-info">
              <text class="action-title">体检提醒</text>
              <text class="action-desc">下次体检时间：2025年9月15日</text>
            </view>
          </view>
          <view class="status-dot bg-red"></view>
        </view>

        <!-- 用药提醒 -->
        <view class="action-card">
          <view class="action-content">
            <view class="action-icon bg-green">
              <text class="iconfont icon-pill text-green"></text>
            </view>
            <view class="action-info">
              <text class="action-title">用药提醒</text>
              <text class="action-desc">今日已完成 2/3 次用药</text>
            </view>
          </view>
          <view class="status-dot bg-yellow"></view>
        </view>

        <!-- 运动计划 -->
        <view class="action-card">
          <view class="action-content">
            <view class="action-icon bg-purple">
              <text class="iconfont icon-target text-purple"></text>
            </view>
            <view class="action-info">
              <text class="action-title">运动计划</text>
              <text class="action-desc">今日目标完成度 65%</text>
            </view>
          </view>
          <view class="status-dot bg-yellow"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HomePage',
  data() {
    return {
      healthMetrics: [
        { 
          title: '心率', 
          value: '72', 
          unit: 'bpm', 
          icon: 'icon-heart', 
          color: 'text-red',
          bgColor: 'bg-red'
        },
        { 
          title: '步数', 
          value: '8,542', 
          unit: '步', 
          icon: 'icon-activity', 
          color: 'text-green',
          bgColor: 'bg-green'
        },
        { 
          title: '卡路里', 
          value: '2,150', 
          unit: 'kcal', 
          icon: 'icon-flame', 
          color: 'text-orange',
          bgColor: 'bg-orange'
        },
        { 
          title: '睡眠', 
          value: '7.5', 
          unit: '小时', 
          icon: 'icon-clock', 
          color: 'text-blue',
          bgColor: 'bg-blue'
        }
      ]
    }
  },
  onLoad() {
    this.loadHealthData()
  },
  methods: {
    loadHealthData() {
      // 加载健康数据
      console.log('加载健康数据')
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部导航留空间 */
}

.container {
  padding: 48rpx 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 48rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 16rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: var(--text-secondary);
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.metric-card {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 48rpx;
  text-align: center;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  
  .metric-icon {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 32rpx;
    
    .iconfont {
      font-size: 48rpx;
    }
  }
  
  .metric-content {
    .metric-value {
      display: block;
      font-size: 48rpx;
      font-weight: 500;
      color: var(--text-color);
      margin-bottom: 8rpx;
    }
    
    .metric-title {
      display: block;
      font-size: 28rpx;
      color: var(--text-secondary);
      margin-bottom: 4rpx;
    }
    
    .metric-unit {
      display: block;
      font-size: 24rpx;
      color: var(--text-secondary);
    }
  }
}

.quick-actions {
  .section-title {
    display: block;
    font-size: 36rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 24rpx;
  }
}

.action-card {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  position: relative;
  
  .action-content {
    display: flex;
    align-items: center;
    
    .action-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      
      .iconfont {
        font-size: 40rpx;
      }
    }
    
    .action-info {
      flex: 1;
      
      .action-title {
        display: block;
        font-size: 32rpx;
        font-weight: 500;
        color: var(--text-color);
        margin-bottom: 8rpx;
      }
      
      .action-desc {
        display: block;
        font-size: 28rpx;
        color: var(--text-secondary);
      }
    }
  }
  
  .status-dot {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
  }
}

/* 颜色类 */
.bg-red { background-color: rgba(255, 59, 48, 0.1); }
.bg-green { background-color: rgba(52, 199, 89, 0.1); }
.bg-orange { background-color: rgba(255, 149, 0, 0.1); }
.bg-blue { background-color: rgba(0, 122, 255, 0.1); }
.bg-purple { background-color: rgba(175, 82, 222, 0.1); }
.bg-yellow { background-color: rgba(255, 204, 0, 0.1); }

.text-red { color: #FF3B30; }
.text-green { color: #34C759; }
.text-orange { color: #FF9500; }
.text-blue { color: #007AFF; }
.text-purple { color: #AF52DE; }
.text-yellow { color: #FFCC00; }
</style>
