module.exports = {
  presets: [
    [
      '@vue/cli-plugin-babel/preset',
      {
        useBuiltIns: 'entry',
        corejs: 3
      }
    ]
  ],
  plugins: [
    [
      'import',
      {
        libraryName: '@dcloudio/uni-ui',
        customName: (name) => {
          return `@dcloudio/uni-ui/lib/${name}/${name}`
        }
      }
    ]
  ],
  env: {
    development: {
      // 开发环境配置
      sourceMaps: true,
      retainLines: true
    },
    production: {
      // 生产环境配置
      plugins: [
        [
          'transform-remove-console',
          {
            exclude: ['error', 'warn']
          }
        ]
      ]
    }
  }
}
