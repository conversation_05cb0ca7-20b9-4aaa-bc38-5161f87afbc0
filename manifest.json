{"name": "健康报告助手", "appid": "__UNI__HEALTH_REPORT", "description": "个人健康报告管理应用", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "modules": {"Camera": {}, "Fingerprint": {}, "SQLite": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CAMERA\" />", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />", "<uses-permission android:name=\"android.permission.USE_FINGERPRINT\" />"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "ios": {"capabilities": ["com.apple.developer.camera"], "privacyDescription": {"NSCameraUsageDescription": "需要访问相机来拍摄健康报告", "NSPhotoLibraryUsageDescription": "需要访问相册来选择健康报告图片"}}}, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}}, "h5": {"title": "健康报告助手", "template": "index.html", "router": {"mode": "hash"}, "optimization": {"treeShaking": {"enable": true}}}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false, "minified": true, "postcss": true}, "usingComponents": true, "permission": {"scope.camera": {"desc": "需要使用相机拍摄健康报告"}, "scope.album": {"desc": "需要访问相册选择健康报告图片"}}}, "quickapp": {}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3"}