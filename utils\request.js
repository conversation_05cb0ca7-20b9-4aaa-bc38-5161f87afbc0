/**
 * 网络请求封装
 */

// 请求配置
const config = {
  baseURL: process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3000/api' 
    : 'https://api.healthreport.com',
  timeout: 10000,
  retryTimes: 3,
  retryDelay: 1000
}

// 请求拦截器
const requestInterceptors = []
const responseInterceptors = []

// 添加请求拦截器
export function addRequestInterceptor(interceptor) {
  requestInterceptors.push(interceptor)
}

// 添加响应拦截器
export function addResponseInterceptor(interceptor) {
  responseInterceptors.push(interceptor)
}

// 默认请求拦截器
addRequestInterceptor((options) => {
  // 添加token
  const token = uni.getStorageSync('token')
  if (token) {
    options.header = options.header || {}
    options.header.Authorization = `Bearer ${token}`
  }
  
  // 添加设备信息
  const systemInfo = uni.getSystemInfoSync()
  options.header = options.header || {}
  options.header['X-Device-Platform'] = systemInfo.platform
  options.header['X-Device-Model'] = systemInfo.model
  options.header['X-App-Version'] = '1.0.0'
  
  return options
})

// 默认响应拦截器
addResponseInterceptor((response) => {
  // 统一错误处理
  if (response.statusCode === 401) {
    // token过期，跳转到登录页
    uni.removeStorageSync('token')
    uni.reLaunch({
      url: '/pages/login/login'
    })
    return Promise.reject(new Error('登录已过期'))
  }
  
  if (response.statusCode >= 400) {
    const error = new Error(response.data?.message || '请求失败')
    error.statusCode = response.statusCode
    return Promise.reject(error)
  }
  
  return response.data
})

// 核心请求函数
async function request(options) {
  // 应用请求拦截器
  for (const interceptor of requestInterceptors) {
    options = await interceptor(options)
  }
  
  // 构建完整URL
  if (!options.url.startsWith('http')) {
    options.url = config.baseURL + options.url
  }
  
  // 设置默认值
  options.timeout = options.timeout || config.timeout
  options.header = options.header || {}
  options.header['Content-Type'] = options.header['Content-Type'] || 'application/json'
  
  let retryCount = 0
  
  while (retryCount <= config.retryTimes) {
    try {
      const response = await new Promise((resolve, reject) => {
        uni.request({
          ...options,
          success: resolve,
          fail: reject
        })
      })
      
      // 应用响应拦截器
      let result = response
      for (const interceptor of responseInterceptors) {
        result = await interceptor(result)
      }
      
      return result
      
    } catch (error) {
      retryCount++
      
      // 如果是网络错误且还有重试次数
      if (retryCount <= config.retryTimes && isNetworkError(error)) {
        console.log(`请求失败，第${retryCount}次重试:`, error.message)
        await delay(config.retryDelay * retryCount) // 指数退避
        continue
      }
      
      // 记录错误
      console.error('请求失败:', {
        url: options.url,
        method: options.method,
        error: error.message,
        retryCount
      })
      
      throw error
    }
  }
}

// 判断是否为网络错误
function isNetworkError(error) {
  return error.errMsg && (
    error.errMsg.includes('timeout') ||
    error.errMsg.includes('network') ||
    error.errMsg.includes('连接')
  )
}

// 延迟函数
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// HTTP方法封装
export const http = {
  get(url, params = {}, options = {}) {
    const queryString = Object.keys(params)
      .filter(key => params[key] !== undefined && params[key] !== null)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    const fullUrl = queryString ? `${url}?${queryString}` : url
    
    return request({
      url: fullUrl,
      method: 'GET',
      ...options
    })
  },

  post(url, data = {}, options = {}) {
    return request({
      url,
      method: 'POST',
      data,
      ...options
    })
  },

  put(url, data = {}, options = {}) {
    return request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  },

  delete(url, options = {}) {
    return request({
      url,
      method: 'DELETE',
      ...options
    })
  },

  // 文件上传
  upload(url, filePath, formData = {}, options = {}) {
    return new Promise((resolve, reject) => {
      const uploadTask = uni.uploadFile({
        url: config.baseURL + url,
        filePath,
        name: 'file',
        formData,
        header: {
          Authorization: `Bearer ${uni.getStorageSync('token')}`
        },
        success: (response) => {
          try {
            const data = JSON.parse(response.data)
            resolve(data)
          } catch (error) {
            resolve(response.data)
          }
        },
        fail: reject,
        ...options
      })
      
      // 返回上传任务，可用于监听进度
      return uploadTask
    })
  },

  // 文件下载
  download(url, options = {}) {
    return new Promise((resolve, reject) => {
      const downloadTask = uni.downloadFile({
        url: config.baseURL + url,
        header: {
          Authorization: `Bearer ${uni.getStorageSync('token')}`
        },
        success: resolve,
        fail: reject,
        ...options
      })
      
      return downloadTask
    })
  }
}

// 网络状态监控
export const networkMonitor = {
  isOnline: true,
  listeners: [],
  
  // 初始化网络监控
  init() {
    // 获取初始网络状态
    uni.getNetworkType({
      success: (res) => {
        this.isOnline = res.networkType !== 'none'
      }
    })
    
    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      const wasOnline = this.isOnline
      this.isOnline = res.isConnected
      
      // 通知监听器
      this.listeners.forEach(listener => {
        listener({
          isOnline: this.isOnline,
          networkType: res.networkType,
          wasOnline
        })
      })
      
      // 网络恢复时的处理
      if (!wasOnline && this.isOnline) {
        this.handleNetworkReconnect()
      }
    })
  },
  
  // 添加网络状态监听器
  addListener(listener) {
    this.listeners.push(listener)
  },
  
  // 移除网络状态监听器
  removeListener(listener) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  },
  
  // 网络重连处理
  handleNetworkReconnect() {
    console.log('网络已恢复，开始同步数据')
    
    // 触发数据同步
    uni.$emit('network-reconnect')
    
    uni.showToast({
      title: '网络已恢复',
      icon: 'success',
      duration: 2000
    })
  }
}

// 请求队列管理（离线支持）
export const requestQueue = {
  queue: [],
  isProcessing: false,
  
  // 添加请求到队列
  add(requestOptions) {
    this.queue.push({
      id: Date.now() + Math.random(),
      options: requestOptions,
      timestamp: Date.now(),
      retryCount: 0
    })
    
    // 保存到本地存储
    this.saveToStorage()
    
    // 如果网络可用，立即处理
    if (networkMonitor.isOnline) {
      this.process()
    }
  },
  
  // 处理队列
  async process() {
    if (this.isProcessing || this.queue.length === 0) return
    
    this.isProcessing = true
    
    while (this.queue.length > 0 && networkMonitor.isOnline) {
      const queueItem = this.queue.shift()
      
      try {
        await request(queueItem.options)
        console.log('队列请求成功:', queueItem.id)
      } catch (error) {
        console.error('队列请求失败:', queueItem.id, error)
        
        // 重试逻辑
        queueItem.retryCount++
        if (queueItem.retryCount < 3) {
          this.queue.push(queueItem)
        }
      }
    }
    
    this.isProcessing = false
    this.saveToStorage()
  },
  
  // 保存队列到本地存储
  saveToStorage() {
    try {
      uni.setStorageSync('requestQueue', this.queue)
    } catch (error) {
      console.error('保存请求队列失败:', error)
    }
  },
  
  // 从本地存储恢复队列
  restoreFromStorage() {
    try {
      const savedQueue = uni.getStorageSync('requestQueue')
      if (savedQueue && Array.isArray(savedQueue)) {
        this.queue = savedQueue
      }
    } catch (error) {
      console.error('恢复请求队列失败:', error)
    }
  },
  
  // 清空队列
  clear() {
    this.queue = []
    this.saveToStorage()
  }
}

// 初始化网络监控和请求队列
networkMonitor.init()
requestQueue.restoreFromStorage()

// 监听网络恢复事件
networkMonitor.addListener((status) => {
  if (status.isOnline && !status.wasOnline) {
    requestQueue.process()
  }
})

export default {
  http,
  networkMonitor,
  requestQueue,
  addRequestInterceptor,
  addResponseInterceptor
}
