import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 应用信息
    appInfo: {
      name: '健康报告助手',
      version: '1.0.0',
      buildNumber: '100'
    },
    
    // 系统信息
    systemInfo: null,
    
    // 网络状态
    networkStatus: {
      isConnected: true,
      networkType: 'unknown'
    },
    
    // 应用状态
    appStatus: {
      isFirstLaunch: false,
      hasShownWelcome: false,
      lastActiveTime: null
    },
    
    // 权限状态
    permissions: {
      camera: false,
      album: false,
      location: false,
      notification: false
    },
    
    // 主题设置
    theme: {
      mode: 'auto', // light, dark, auto
      primaryColor: '#007AFF'
    },
    
    // 加载状态
    globalLoading: false,
    
    // 错误信息
    lastError: null
  }),

  getters: {
    // 是否为深色模式
    isDarkMode: (state) => {
      if (state.theme.mode === 'auto') {
        // 根据系统设置判断
        return state.systemInfo?.theme === 'dark'
      }
      return state.theme.mode === 'dark'
    },
    
    // 是否为移动端
    isMobile: (state) => {
      return state.systemInfo?.platform !== 'devtools'
    },
    
    // 是否为小程序
    isMiniProgram: (state) => {
      return state.systemInfo?.uniPlatform?.startsWith('mp-')
    },
    
    // 是否为APP
    isApp: (state) => {
      return state.systemInfo?.uniPlatform === 'app-plus'
    },
    
    // 是否为H5
    isH5: (state) => {
      return state.systemInfo?.uniPlatform === 'h5'
    }
  },

  actions: {
    // 初始化应用
    async initApp() {
      try {
        // 获取系统信息
        await this.getSystemInfo()
        
        // 检查网络状态
        await this.checkNetworkStatus()
        
        // 检查权限状态
        await this.checkPermissions()
        
        // 检查是否首次启动
        this.checkFirstLaunch()
        
        // 设置网络状态监听
        this.setupNetworkListener()
        
        console.log('应用初始化完成')
        
      } catch (error) {
        console.error('应用初始化失败:', error)
        this.lastError = error
      }
    },

    // 获取系统信息
    async getSystemInfo() {
      return new Promise((resolve) => {
        uni.getSystemInfo({
          success: (res) => {
            this.systemInfo = res
            resolve(res)
          },
          fail: (error) => {
            console.error('获取系统信息失败:', error)
            resolve(null)
          }
        })
      })
    },

    // 检查网络状态
    async checkNetworkStatus() {
      return new Promise((resolve) => {
        uni.getNetworkType({
          success: (res) => {
            this.networkStatus = {
              isConnected: res.networkType !== 'none',
              networkType: res.networkType
            }
            resolve(res)
          },
          fail: (error) => {
            console.error('获取网络状态失败:', error)
            resolve(null)
          }
        })
      })
    },

    // 设置网络状态监听
    setupNetworkListener() {
      uni.onNetworkStatusChange((res) => {
        this.networkStatus = {
          isConnected: res.isConnected,
          networkType: res.networkType
        }
        
        if (!res.isConnected) {
          uni.showToast({
            title: '网络连接已断开',
            icon: 'none'
          })
        } else {
          // 网络恢复，尝试同步数据
          this.handleNetworkReconnect()
        }
      })
    },

    // 网络重连处理
    async handleNetworkReconnect() {
      console.log('网络已恢复，开始同步数据')
      // 触发数据同步
      // 这里可以调用其他store的同步方法
    },

    // 检查权限状态
    async checkPermissions() {
      // #ifdef APP-PLUS
      // 检查相机权限
      uni.authorize({
        scope: 'scope.camera',
        success: () => {
          this.permissions.camera = true
        },
        fail: () => {
          this.permissions.camera = false
        }
      })
      
      // 检查相册权限
      uni.authorize({
        scope: 'scope.album',
        success: () => {
          this.permissions.album = true
        },
        fail: () => {
          this.permissions.album = false
        }
      })
      // #endif
      
      // #ifdef MP-WEIXIN
      // 小程序权限检查
      uni.getSetting({
        success: (res) => {
          this.permissions.camera = res.authSetting['scope.camera'] || false
          this.permissions.album = res.authSetting['scope.album'] || false
        }
      })
      // #endif
    },

    // 请求权限
    async requestPermission(scope) {
      return new Promise((resolve, reject) => {
        uni.authorize({
          scope: `scope.${scope}`,
          success: () => {
            this.permissions[scope] = true
            resolve(true)
          },
          fail: () => {
            // 权限被拒绝，引导用户到设置页面
            uni.showModal({
              title: '权限申请',
              content: `需要${scope}权限才能正常使用功能，请到设置页面开启`,
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting[`scope.${scope}`]) {
                        this.permissions[scope] = true
                        resolve(true)
                      } else {
                        reject(new Error('用户拒绝授权'))
                      }
                    }
                  })
                } else {
                  reject(new Error('用户拒绝授权'))
                }
              }
            })
          }
        })
      })
    },

    // 检查是否首次启动
    checkFirstLaunch() {
      const hasLaunched = uni.getStorageSync('hasLaunched')
      this.appStatus.isFirstLaunch = !hasLaunched
      
      if (!hasLaunched) {
        uni.setStorageSync('hasLaunched', true)
      }
    },

    // 设置主题
    setTheme(mode) {
      this.theme.mode = mode
      uni.setStorageSync('themeMode', mode)
      
      // 应用主题
      this.applyTheme()
    },

    // 应用主题
    applyTheme() {
      const isDark = this.isDarkMode
      
      // #ifdef H5
      if (isDark) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
      // #endif
    },

    // 设置全局加载状态
    setGlobalLoading(loading) {
      this.globalLoading = loading
      
      if (loading) {
        uni.showLoading({ title: '加载中...' })
      } else {
        uni.hideLoading()
      }
    },

    // 记录错误
    recordError(error, context = '') {
      this.lastError = {
        message: error.message,
        stack: error.stack,
        context,
        timestamp: new Date().toISOString()
      }
      
      console.error('应用错误:', error, context)
      
      // 上报错误（生产环境）
      if (process.env.NODE_ENV === 'production') {
        this.reportError(this.lastError)
      }
    },

    // 上报错误
    async reportError(errorInfo) {
      try {
        // 模拟错误上报
        console.log('上报错误:', errorInfo)
      } catch (error) {
        console.error('错误上报失败:', error)
      }
    },

    // 更新最后活跃时间
    updateLastActiveTime() {
      this.appStatus.lastActiveTime = new Date().toISOString()
      uni.setStorageSync('lastActiveTime', this.appStatus.lastActiveTime)
    },

    // 从本地存储恢复状态
    restoreFromStorage() {
      try {
        const themeMode = uni.getStorageSync('themeMode')
        if (themeMode) {
          this.theme.mode = themeMode
        }
        
        const lastActiveTime = uni.getStorageSync('lastActiveTime')
        if (lastActiveTime) {
          this.appStatus.lastActiveTime = lastActiveTime
        }
        
        this.applyTheme()
        
      } catch (error) {
        console.error('恢复应用状态失败:', error)
      }
    }
  }
})
