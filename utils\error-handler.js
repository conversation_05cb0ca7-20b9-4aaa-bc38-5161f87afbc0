/**
 * 全局错误处理工具
 */

import { secureLogger } from '@/utils/security/encrypt'

// 错误类型枚举
export const ErrorTypes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  API_ERROR: 'API_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  OCR_ERROR: 'OCR_ERROR',
  STORAGE_ERROR: 'STORAGE_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

// 自定义错误类
export class AppError extends Error {
  constructor(message, type = ErrorTypes.UNKNOWN_ERROR, code = 0, details = {}) {
    super(message)
    this.name = 'AppError'
    this.type = type
    this.code = code
    this.details = details
    this.timestamp = new Date().toISOString()
  }
}

// 网络错误类
export class NetworkError extends AppError {
  constructor(message, code = 0, details = {}) {
    super(message, ErrorTypes.NETWORK_ERROR, code, details)
    this.name = 'NetworkError'
  }
}

// API错误类
export class APIError extends AppError {
  constructor(message, code = 0, details = {}) {
    super(message, ErrorTypes.API_ERROR, code, details)
    this.name = 'APIError'
  }
}

// 验证错误类
export class ValidationError extends AppError {
  constructor(message, field = '', details = {}) {
    super(message, ErrorTypes.VALIDATION_ERROR, 0, { field, ...details })
    this.name = 'ValidationError'
  }
}

// 权限错误类
export class PermissionError extends AppError {
  constructor(message, permission = '', details = {}) {
    super(message, ErrorTypes.PERMISSION_ERROR, 0, { permission, ...details })
    this.name = 'PermissionError'
  }
}

// OCR错误类
export class OCRError extends AppError {
  constructor(message, details = {}) {
    super(message, ErrorTypes.OCR_ERROR, 0, details)
    this.name = 'OCRError'
  }
}

// 存储错误类
export class StorageError extends AppError {
  constructor(message, details = {}) {
    super(message, ErrorTypes.STORAGE_ERROR, 0, details)
    this.name = 'StorageError'
  }
}

// 错误处理器类
export class ErrorHandler {
  constructor() {
    this.errorQueue = []
    this.maxQueueSize = 100
    this.reportInterval = 30000 // 30秒上报一次
    this.setupGlobalErrorHandling()
    this.startErrorReporting()
  }

  // 设置全局错误处理
  setupGlobalErrorHandling() {
    // Vue错误处理
    if (typeof window !== 'undefined' && window.Vue) {
      window.Vue.config.errorHandler = (err, vm, info) => {
        this.handleError(err, { context: 'Vue', info, vm })
      }
    }

    // Promise未捕获错误
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(event.reason, { context: 'Promise' })
      })
    }

    // uni-app错误处理
    if (typeof uni !== 'undefined') {
      uni.onError((error) => {
        this.handleError(new Error(error), { context: 'uni-app' })
      })
    }
  }

  // 处理错误
  handleError(error, context = {}) {
    try {
      // 标准化错误对象
      const standardError = this.standardizeError(error, context)
      
      // 记录错误
      this.logError(standardError)
      
      // 添加到队列
      this.addToQueue(standardError)
      
      // 显示用户友好的错误信息
      this.showUserError(standardError)
      
      // 记录安全事件
      secureLogger.logSecurityEvent('error_occurred', {
        type: standardError.type,
        message: standardError.message,
        context: standardError.context
      })
      
    } catch (handlerError) {
      console.error('错误处理器本身出错:', handlerError)
    }
  }

  // 标准化错误对象
  standardizeError(error, context = {}) {
    if (error instanceof AppError) {
      return {
        ...error,
        context: { ...error.details, ...context }
      }
    }

    // 网络错误检测
    if (this.isNetworkError(error)) {
      return new NetworkError(
        this.getNetworkErrorMessage(error),
        error.code || 0,
        { originalError: error.message, ...context }
      )
    }

    // API错误检测
    if (this.isAPIError(error)) {
      return new APIError(
        error.message || 'API请求失败',
        error.status || error.statusCode || 0,
        { ...context }
      )
    }

    // 通用错误
    return new AppError(
      error.message || '未知错误',
      ErrorTypes.UNKNOWN_ERROR,
      0,
      { originalError: error, ...context }
    )
  }

  // 检测网络错误
  isNetworkError(error) {
    const networkKeywords = [
      'network', 'timeout', 'connection', 'offline',
      'dns', 'unreachable', 'failed to fetch'
    ]
    
    const errorMessage = (error.message || '').toLowerCase()
    return networkKeywords.some(keyword => errorMessage.includes(keyword))
  }

  // 检测API错误
  isAPIError(error) {
    return error.status || error.statusCode || error.response
  }

  // 获取网络错误信息
  getNetworkErrorMessage(error) {
    if (error.message?.includes('timeout')) {
      return '网络请求超时，请检查网络连接'
    }
    if (error.message?.includes('offline')) {
      return '网络连接已断开，请检查网络设置'
    }
    return '网络连接异常，请稍后重试'
  }

  // 记录错误
  logError(error) {
    const logLevel = this.getLogLevel(error)
    const logMessage = this.formatLogMessage(error)
    
    switch (logLevel) {
      case 'error':
        console.error(logMessage, error)
        break
      case 'warn':
        console.warn(logMessage, error)
        break
      default:
        console.log(logMessage, error)
    }
  }

  // 获取日志级别
  getLogLevel(error) {
    switch (error.type) {
      case ErrorTypes.NETWORK_ERROR:
      case ErrorTypes.API_ERROR:
        return 'error'
      case ErrorTypes.VALIDATION_ERROR:
        return 'warn'
      default:
        return 'error'
    }
  }

  // 格式化日志信息
  formatLogMessage(error) {
    return `[${error.type}] ${error.message} (Code: ${error.code})`
  }

  // 添加到错误队列
  addToQueue(error) {
    this.errorQueue.push({
      ...error,
      id: this.generateErrorId(),
      timestamp: new Date().toISOString()
    })

    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift()
    }
  }

  // 生成错误ID
  generateErrorId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 显示用户错误信息
  showUserError(error) {
    const userMessage = this.getUserMessage(error)
    
    // 避免重复显示相同错误
    if (this.lastShownError === userMessage) {
      return
    }
    this.lastShownError = userMessage
    
    // 清除重复检查
    setTimeout(() => {
      this.lastShownError = null
    }, 3000)

    // 显示错误提示
    if (typeof uni !== 'undefined') {
      uni.showToast({
        title: userMessage,
        icon: 'none',
        duration: 3000
      })
    }
  }

  // 获取用户友好的错误信息
  getUserMessage(error) {
    const userMessages = {
      [ErrorTypes.NETWORK_ERROR]: '网络连接异常，请检查网络设置',
      [ErrorTypes.API_ERROR]: '服务器繁忙，请稍后重试',
      [ErrorTypes.VALIDATION_ERROR]: '输入信息有误，请检查后重试',
      [ErrorTypes.PERMISSION_ERROR]: '权限不足，请检查应用权限设置',
      [ErrorTypes.OCR_ERROR]: '图片识别失败，请重新拍照或手动输入',
      [ErrorTypes.STORAGE_ERROR]: '数据保存失败，请检查存储空间',
      [ErrorTypes.UNKNOWN_ERROR]: '操作失败，请稍后重试'
    }

    return userMessages[error.type] || error.message || '未知错误'
  }

  // 开始错误上报
  startErrorReporting() {
    setInterval(() => {
      this.reportErrors()
    }, this.reportInterval)
  }

  // 上报错误
  async reportErrors() {
    if (this.errorQueue.length === 0) return

    try {
      const errors = [...this.errorQueue]
      this.errorQueue = []

      // 模拟错误上报
      console.log('上报错误:', errors.length, '条')
      
      // 实际项目中应该调用错误上报API
      // await this.sendErrorReport(errors)
      
    } catch (error) {
      console.error('错误上报失败:', error)
      // 上报失败的错误重新加入队列
      this.errorQueue.unshift(...this.errorQueue)
    }
  }

  // 发送错误报告
  async sendErrorReport(errors) {
    // 实际的错误上报实现
    // 可以发送到Sentry、Bugsnag等错误监控服务
    return Promise.resolve()
  }

  // 获取错误统计
  getErrorStats() {
    const stats = {}
    
    this.errorQueue.forEach(error => {
      stats[error.type] = (stats[error.type] || 0) + 1
    })
    
    return {
      total: this.errorQueue.length,
      byType: stats,
      recent: this.errorQueue.slice(-10)
    }
  }

  // 清除错误队列
  clearErrorQueue() {
    this.errorQueue = []
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler()

// 便捷的错误处理函数
export function handleError(error, context = {}) {
  errorHandler.handleError(error, context)
}

// 异步函数错误包装器
export function withErrorHandling(asyncFn, context = {}) {
  return async (...args) => {
    try {
      return await asyncFn(...args)
    } catch (error) {
      handleError(error, { ...context, function: asyncFn.name })
      throw error
    }
  }
}

// 重试机制
export async function withRetry(asyncFn, maxRetries = 3, delay = 1000) {
  let lastError
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await asyncFn()
    } catch (error) {
      lastError = error
      
      if (i === maxRetries) {
        handleError(error, { retryAttempt: i + 1 })
        throw error
      }
      
      // 指数退避
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }
}

// 默认导出
export default {
  ErrorHandler,
  errorHandler,
  handleError,
  withErrorHandling,
  withRetry,
  ErrorTypes,
  AppError,
  NetworkError,
  APIError,
  ValidationError,
  PermissionError,
  OCRError,
  StorageError
}
