/**
 * 数据加密工具
 * 注意：在实际项目中需要安装crypto-js库
 * npm install crypto-js
 */

// 简化版加密实现（用于演示）
// 在实际项目中应该使用crypto-js库

export const encryptionService = {
  // 简单的Base64编码（仅用于演示，实际应使用AES加密）
  encrypt(data, key) {
    try {
      const jsonString = JSON.stringify(data)
      const encoded = this.base64Encode(jsonString + key)
      return encoded
    } catch (error) {
      console.error('加密失败:', error)
      return null
    }
  },

  // 简单的Base64解码
  decrypt(encryptedData, key) {
    try {
      const decoded = this.base64Decode(encryptedData)
      const jsonString = decoded.replace(key, '')
      return JSON.parse(jsonString)
    } catch (error) {
      console.error('解密失败:', error)
      return null
    }
  },

  // Base64编码
  base64Encode(str) {
    // #ifdef H5
    return btoa(unescape(encodeURIComponent(str)))
    // #endif
    
    // #ifndef H5
    // uni-app环境下的Base64编码
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
    let result = ''
    let i = 0
    
    while (i < str.length) {
      const a = str.charCodeAt(i++)
      const b = i < str.length ? str.charCodeAt(i++) : 0
      const c = i < str.length ? str.charCodeAt(i++) : 0
      
      const bitmap = (a << 16) | (b << 8) | c
      
      result += chars.charAt((bitmap >> 18) & 63)
      result += chars.charAt((bitmap >> 12) & 63)
      result += i - 2 < str.length ? chars.charAt((bitmap >> 6) & 63) : '='
      result += i - 1 < str.length ? chars.charAt(bitmap & 63) : '='
    }
    
    return result
    // #endif
  },

  // Base64解码
  base64Decode(str) {
    // #ifdef H5
    return decodeURIComponent(escape(atob(str)))
    // #endif
    
    // #ifndef H5
    // uni-app环境下的Base64解码
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
    let result = ''
    let i = 0
    
    str = str.replace(/[^A-Za-z0-9+/]/g, '')
    
    while (i < str.length) {
      const encoded1 = chars.indexOf(str.charAt(i++))
      const encoded2 = chars.indexOf(str.charAt(i++))
      const encoded3 = chars.indexOf(str.charAt(i++))
      const encoded4 = chars.indexOf(str.charAt(i++))
      
      const bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4
      
      result += String.fromCharCode((bitmap >> 16) & 255)
      if (encoded3 !== 64) result += String.fromCharCode((bitmap >> 8) & 255)
      if (encoded4 !== 64) result += String.fromCharCode(bitmap & 255)
    }
    
    return result
    // #endif
  },

  // 生成随机密钥
  generateKey(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },

  // 哈希函数（简化版）
  hash(str) {
    let hash = 0
    if (str.length === 0) return hash.toString()
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  },

  // 验证数据完整性
  verifyIntegrity(data, expectedHash) {
    const dataHash = this.hash(JSON.stringify(data))
    return dataHash === expectedHash
  }
}

// 密码强度检查
export function checkPasswordStrength(password) {
  const checks = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
  
  const score = Object.values(checks).filter(Boolean).length
  
  let strength = 'weak'
  if (score >= 4) strength = 'strong'
  else if (score >= 3) strength = 'medium'
  
  return {
    score,
    strength,
    checks,
    suggestions: generatePasswordSuggestions(checks)
  }
}

function generatePasswordSuggestions(checks) {
  const suggestions = []
  
  if (!checks.length) suggestions.push('密码长度至少8位')
  if (!checks.lowercase) suggestions.push('包含小写字母')
  if (!checks.uppercase) suggestions.push('包含大写字母')
  if (!checks.number) suggestions.push('包含数字')
  if (!checks.special) suggestions.push('包含特殊字符')
  
  return suggestions
}

// 敏感信息脱敏
export function maskSensitiveInfo(str, type = 'phone') {
  if (!str) return ''
  
  switch (type) {
    case 'phone':
      return str.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    case 'email':
      return str.replace(/(.{2}).*(@.*)/, '$1****$2')
    case 'idCard':
      return str.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    case 'name':
      if (str.length <= 2) return str
      return str.charAt(0) + '*'.repeat(str.length - 2) + str.charAt(str.length - 1)
    default:
      return str
  }
}

// 安全存储
export const secureStorage = {
  // 安全设置
  setSecure(key, value) {
    try {
      const encryptedValue = encryptionService.encrypt(value, key)
      uni.setStorageSync(key, encryptedValue)
      return true
    } catch (error) {
      console.error('安全存储失败:', error)
      return false
    }
  },
  
  // 安全获取
  getSecure(key) {
    try {
      const encryptedValue = uni.getStorageSync(key)
      if (!encryptedValue) return null
      
      return encryptionService.decrypt(encryptedValue, key)
    } catch (error) {
      console.error('安全获取失败:', error)
      return null
    }
  },
  
  // 安全移除
  removeSecure(key) {
    try {
      uni.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('安全移除失败:', error)
      return false
    }
  }
}

// 设备指纹
export function getDeviceFingerprint() {
  const systemInfo = uni.getSystemInfoSync()
  
  const fingerprint = {
    platform: systemInfo.platform,
    system: systemInfo.system,
    model: systemInfo.model,
    brand: systemInfo.brand,
    screenWidth: systemInfo.screenWidth,
    screenHeight: systemInfo.screenHeight,
    pixelRatio: systemInfo.pixelRatio,
    language: systemInfo.language
  }
  
  // 生成指纹哈希
  const fingerprintStr = JSON.stringify(fingerprint)
  return encryptionService.hash(fingerprintStr)
}

// 安全日志
export const secureLogger = {
  // 记录安全事件
  logSecurityEvent(event, details = {}) {
    const logEntry = {
      event,
      timestamp: new Date().toISOString(),
      deviceFingerprint: getDeviceFingerprint(),
      details: this.sanitizeDetails(details)
    }
    
    console.log('安全事件:', logEntry)
    
    // 在生产环境中应该上报到安全监控系统
    if (process.env.NODE_ENV === 'production') {
      this.reportSecurityEvent(logEntry)
    }
  },
  
  // 清理敏感信息
  sanitizeDetails(details) {
    const sanitized = { ...details }
    
    // 移除敏感字段
    const sensitiveFields = ['password', 'token', 'phone', 'idCard', 'email']
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        delete sanitized[field]
      }
    })
    
    return sanitized
  },
  
  // 上报安全事件
  async reportSecurityEvent(logEntry) {
    try {
      // 模拟上报到安全监控系统
      console.log('上报安全事件:', logEntry)
    } catch (error) {
      console.error('安全事件上报失败:', error)
    }
  }
}

export default {
  encryptionService,
  checkPasswordStrength,
  maskSensitiveInfo,
  secureStorage,
  getDeviceFingerprint,
  secureLogger
}
