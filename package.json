{"name": "health-report-app", "version": "1.0.0", "description": "个人健康报告管理应用", "scripts": {"serve": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "sass": "^1.49.9"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}, "repository": {"type": "git", "url": "https://github.com/your-username/health-report-app.git"}, "keywords": ["health", "report", "medical", "uni-app", "vue3", "mobile"], "author": "Your Name", "license": "MIT", "bugs": {"url": "https://github.com/your-username/health-report-app/issues"}, "homepage": "https://github.com/your-username/health-report-app#readme"}