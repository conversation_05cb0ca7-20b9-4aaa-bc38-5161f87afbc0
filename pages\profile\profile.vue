<template>
  <view class="profile-page">
    <view class="container">
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="user-avatar" @click="changeAvatar">
          <image 
            :src="userInfo.avatar || '/static/images/default-avatar.png'" 
            class="avatar-image"
            mode="aspectFill"
          />
          <view class="avatar-edit">
            <text class="iconfont icon-camera"></text>
          </view>
        </view>
        <view class="user-info">
          <text class="user-name">{{ userInfo.nickname || '未设置昵称' }}</text>
          <text class="user-phone">{{ userInfo.phone }}</text>
        </view>
        <view class="user-stats">
          <view class="stat-item">
            <text class="stat-value">{{ userStats.totalReports }}</text>
            <text class="stat-label">报告数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ userStats.daysUsed }}</text>
            <text class="stat-label">使用天数</text>
          </view>
        </view>
      </view>

      <!-- 功能菜单 -->
      <view class="menu-section">
        <view class="menu-group">
          <view 
            v-for="item in personalMenus" 
            :key="item.key"
            class="menu-item"
            @click="handleMenuClick(item.key)"
          >
            <view class="menu-icon">
              <text :class="['iconfont', item.icon, item.color]"></text>
            </view>
            <text class="menu-title">{{ item.title }}</text>
            <text class="iconfont icon-chevron-right menu-arrow"></text>
          </view>
        </view>

        <view class="menu-group">
          <view 
            v-for="item in settingMenus" 
            :key="item.key"
            class="menu-item"
            @click="handleMenuClick(item.key)"
          >
            <view class="menu-icon">
              <text :class="['iconfont', item.icon, item.color]"></text>
            </view>
            <text class="menu-title">{{ item.title }}</text>
            <switch 
              v-if="item.type === 'switch'"
              :checked="item.checked"
              @change="handleSwitchChange(item.key, $event)"
              class="menu-switch"
            />
            <text 
              v-else
              class="iconfont icon-chevron-right menu-arrow"
            ></text>
          </view>
        </view>

        <view class="menu-group">
          <view 
            v-for="item in aboutMenus" 
            :key="item.key"
            class="menu-item"
            @click="handleMenuClick(item.key)"
          >
            <view class="menu-icon">
              <text :class="['iconfont', item.icon, item.color]"></text>
            </view>
            <text class="menu-title">{{ item.title }}</text>
            <text class="iconfont icon-chevron-right menu-arrow"></text>
          </view>
        </view>
      </view>

      <!-- 退出登录按钮 -->
      <view class="logout-section">
        <button class="logout-btn" @click="logout">
          退出登录
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { useUserStore } from '@/stores/user'

export default {
  name: 'ProfilePage',
  data() {
    return {
      userInfo: {
        nickname: '健康用户',
        phone: '138****8888',
        avatar: ''
      },
      userStats: {
        totalReports: 12,
        daysUsed: 45
      },
      personalMenus: [
        {
          key: 'editProfile',
          title: '编辑资料',
          icon: 'icon-user',
          color: 'text-blue'
        },
        {
          key: 'healthProfile',
          title: '健康档案',
          icon: 'icon-heart',
          color: 'text-red'
        },
        {
          key: 'dataExport',
          title: '数据导出',
          icon: 'icon-download',
          color: 'text-green'
        }
      ],
      settingMenus: [
        {
          key: 'biometric',
          title: '生物识别',
          icon: 'icon-fingerprint',
          color: 'text-purple',
          type: 'switch',
          checked: false
        },
        {
          key: 'autoSync',
          title: '自动同步',
          icon: 'icon-sync',
          color: 'text-blue',
          type: 'switch',
          checked: true
        },
        {
          key: 'notification',
          title: '消息通知',
          icon: 'icon-bell',
          color: 'text-orange',
          type: 'switch',
          checked: true
        },
        {
          key: 'privacy',
          title: '隐私设置',
          icon: 'icon-shield',
          color: 'text-gray'
        }
      ],
      aboutMenus: [
        {
          key: 'help',
          title: '帮助中心',
          icon: 'icon-help',
          color: 'text-blue'
        },
        {
          key: 'feedback',
          title: '意见反馈',
          icon: 'icon-message',
          color: 'text-green'
        },
        {
          key: 'about',
          title: '关于我们',
          icon: 'icon-info',
          color: 'text-gray'
        }
      ]
    }
  },
  onLoad() {
    this.loadUserInfo()
  },
  methods: {
    loadUserInfo() {
      // 加载用户信息
      const userStore = useUserStore()
      this.userInfo = userStore.userInfo || this.userInfo
    },
    
    changeAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          // 上传头像
          this.uploadAvatar(tempFilePath)
        }
      })
    },
    
    uploadAvatar(filePath) {
      // 上传头像逻辑
      console.log('上传头像:', filePath)
    },
    
    handleMenuClick(key) {
      switch (key) {
        case 'editProfile':
          uni.navigateTo({ url: '/pages/profile/edit' })
          break
        case 'healthProfile':
          uni.navigateTo({ url: '/pages/profile/health' })
          break
        case 'dataExport':
          this.exportData()
          break
        case 'privacy':
          uni.navigateTo({ url: '/pages/profile/privacy' })
          break
        case 'help':
          uni.navigateTo({ url: '/pages/profile/help' })
          break
        case 'feedback':
          uni.navigateTo({ url: '/pages/profile/feedback' })
          break
        case 'about':
          uni.navigateTo({ url: '/pages/profile/about' })
          break
      }
    },
    
    handleSwitchChange(key, event) {
      const checked = event.detail.value
      console.log(`${key} 开关状态:`, checked)
      
      // 更新对应设置
      const menu = this.settingMenus.find(item => item.key === key)
      if (menu) {
        menu.checked = checked
      }
    },
    
    exportData() {
      uni.showLoading({ title: '导出中...' })
      
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '导出成功',
          icon: 'success'
        })
      }, 2000)
    },
    
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除用户数据
            uni.clearStorageSync()
            // 跳转到登录页
            uni.reLaunch({
              url: '/pages/login/login'
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.container {
  padding: 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.user-card {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 48rpx;
  text-align: center;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  
  .user-avatar {
    position: relative;
    width: 160rpx;
    height: 160rpx;
    margin: 0 auto 32rpx;
    
    .avatar-image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background-color: var(--background-color);
    }
    
    .avatar-edit {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 48rpx;
      height: 48rpx;
      background-color: var(--primary-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 4rpx solid var(--card-background);
      
      .iconfont {
        font-size: 24rpx;
        color: #ffffff;
      }
    }
  }
  
  .user-info {
    margin-bottom: 32rpx;
    
    .user-name {
      display: block;
      font-size: 36rpx;
      font-weight: 500;
      color: var(--text-color);
      margin-bottom: 8rpx;
    }
    
    .user-phone {
      display: block;
      font-size: 28rpx;
      color: var(--text-secondary);
    }
  }
  
  .user-stats {
    display: flex;
    justify-content: center;
    gap: 64rpx;
    
    .stat-item {
      text-align: center;
      
      .stat-value {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        display: block;
        font-size: 24rpx;
        color: var(--text-secondary);
      }
    }
  }
}

.menu-section {
  .menu-group {
    background-color: var(--card-background);
    border-radius: 16rpx;
    margin-bottom: 32rpx;
    overflow: hidden;
    box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  }
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.3s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: var(--background-color);
  }
  
  .menu-icon {
    width: 72rpx;
    height: 72rpx;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    background-color: rgba(0, 122, 255, 0.1);
    
    .iconfont {
      font-size: 36rpx;
    }
  }
  
  .menu-title {
    flex: 1;
    font-size: 32rpx;
    color: var(--text-color);
  }
  
  .menu-arrow {
    font-size: 32rpx;
    color: var(--text-secondary);
  }
  
  .menu-switch {
    transform: scale(0.8);
  }
}

.logout-section {
  margin-top: 48rpx;
}

.logout-btn {
  width: 100%;
  background-color: var(--error-color);
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 500;
  
  &:active {
    opacity: 0.8;
  }
}
</style>
