/* uni-app全局样式变量 */

/* 颜色变量 */
$uni-color-primary: #007AFF;
$uni-color-success: #34C759;
$uni-color-warning: #FF9500;
$uni-color-error: #FF3B30;

$uni-text-color: #333333;
$uni-text-color-grey: #8E8E93;
$uni-text-color-placeholder: #C0C4CC;

$uni-bg-color: #F2F2F7;
$uni-bg-color-grey: #F8F8F8;
$uni-bg-color-hover: #F1F1F1;
$uni-bg-color-mask: rgba(0, 0, 0, 0.4);

$uni-border-color: #E5E5E5;
$uni-border-color-light: #F0F0F0;

/* 尺寸变量 */
$uni-spacing-sm: 16rpx;
$uni-spacing-base: 24rpx;
$uni-spacing-lg: 32rpx;
$uni-spacing-xl: 48rpx;

$uni-border-radius-sm: 8rpx;
$uni-border-radius-base: 12rpx;
$uni-border-radius-lg: 16rpx;
$uni-border-radius-circle: 50%;

$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;
$uni-font-size-xl: 36rpx;

/* 阴影变量 */
$uni-shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
$uni-shadow-base: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$uni-shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);

/* 动画变量 */
$uni-animation-duration-fast: 0.2s;
$uni-animation-duration-base: 0.3s;
$uni-animation-duration-slow: 0.5s;

/* 层级变量 */
$uni-z-index-toast: 10090;
$uni-z-index-modal: 10075;
$uni-z-index-popup: 10070;
$uni-z-index-mask: 10060;
$uni-z-index-navbar: 980;
$uni-z-index-tabbar: 950;
