<template>
  <view class="add-report-page">
    <view class="container">
      <!-- 步骤指示器 -->
      <view class="steps-indicator">
        <view 
          v-for="(step, index) in steps" 
          :key="step.key"
          :class="['step-item', { 
            active: currentStep === index,
            completed: currentStep > index 
          }]"
        >
          <view class="step-circle">
            <text v-if="currentStep > index" class="iconfont icon-check"></text>
            <text v-else class="step-number">{{ index + 1 }}</text>
          </view>
          <text class="step-title">{{ step.title }}</text>
        </view>
      </view>
      
      <!-- 步骤内容 -->
      <view class="step-content">
        <!-- 步骤1: 上传图片 -->
        <view v-if="currentStep === 0" class="step-upload">
          <text class="step-desc">请上传您的健康报告图片</text>
          <camera-upload
            ref="cameraUpload"
            :auto-o-c-r="true"
            @image-selected="handleImageSelected"
            @ocr-success="handleOCRSuccess"
            @ocr-complete="handleOCRComplete"
            @manual-input-required="goToManualInput"
          />
        </view>
        
        <!-- 步骤2: 确认信息 -->
        <view v-if="currentStep === 1" class="step-confirm">
          <text class="step-desc">请确认或修改识别结果</text>
          
          <!-- 基本信息表单 -->
          <view class="form-section">
            <text class="section-title">基本信息</text>
            
            <custom-input
              v-model="reportForm.title"
              label="报告标题"
              placeholder="请输入报告标题"
              required
            />
            
            <custom-input
              v-model="reportForm.hospital"
              label="医院名称"
              placeholder="请输入医院名称"
              prefix-icon="icon-hospital"
              required
            />
            
            <custom-input
              v-model="reportForm.doctor"
              label="医生姓名"
              placeholder="请输入医生姓名"
              prefix-icon="icon-user"
            />
            
            <view class="date-inputs">
              <view class="date-input-group">
                <text class="input-label">检查日期</text>
                <picker 
                  mode="date" 
                  :value="reportForm.checkDate"
                  @change="handleDateChange('checkDate', $event)"
                >
                  <view class="date-picker">
                    <text class="date-text">
                      {{ reportForm.checkDate || '请选择日期' }}
                    </text>
                    <text class="iconfont icon-calendar"></text>
                  </view>
                </picker>
              </view>
              
              <view class="date-input-group">
                <text class="input-label">报告日期</text>
                <picker 
                  mode="date" 
                  :value="reportForm.reportDate"
                  @change="handleDateChange('reportDate', $event)"
                >
                  <view class="date-picker">
                    <text class="date-text">
                      {{ reportForm.reportDate || '请选择日期' }}
                    </text>
                    <text class="iconfont icon-calendar"></text>
                  </view>
                </picker>
              </view>
            </view>
          </view>
          
          <!-- 检查项目列表 -->
          <view class="form-section">
            <view class="section-header">
              <text class="section-title">检查项目</text>
              <custom-button 
                type="text" 
                size="small"
                icon="icon-plus"
                @click="addHealthItem"
              >
                添加项目
              </custom-button>
            </view>
            
            <view class="health-items-list">
              <view 
                v-for="(item, index) in reportForm.items" 
                :key="item.id"
                class="health-item-form"
              >
                <view class="item-header">
                  <text class="item-index">{{ index + 1 }}</text>
                  <text 
                    class="iconfont icon-delete item-delete"
                    @click="removeHealthItem(index)"
                  ></text>
                </view>
                
                <view class="item-inputs">
                  <custom-input
                    v-model="item.name"
                    placeholder="项目名称"
                    size="small"
                  />
                  
                  <view class="value-unit-group">
                    <custom-input
                      v-model="item.value"
                      placeholder="检查结果"
                      type="number"
                      size="small"
                      class="value-input"
                    />
                    <custom-input
                      v-model="item.unit"
                      placeholder="单位"
                      size="small"
                      class="unit-input"
                    />
                  </view>
                  
                  <custom-input
                    v-model="item.referenceRange"
                    placeholder="参考范围"
                    size="small"
                  />
                  
                  <view class="item-options">
                    <picker 
                      :range="categories"
                      :value="getCategoryIndex(item.category)"
                      @change="handleCategoryChange(index, $event)"
                    >
                      <view class="category-picker">
                        <text class="category-text">
                          {{ item.category || '选择分类' }}
                        </text>
                        <text class="iconfont icon-chevron-down"></text>
                      </view>
                    </picker>
                    
                    <view class="abnormal-switch">
                      <text class="switch-label">异常</text>
                      <switch 
                        :checked="item.isAbnormal"
                        @change="handleAbnormalChange(index, $event)"
                        color="#FF3B30"
                      />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 备注 -->
          <view class="form-section">
            <custom-input
              v-model="reportForm.notes"
              type="textarea"
              label="备注"
              placeholder="请输入备注信息（可选）"
              :auto-height="true"
            />
          </view>
        </view>
        
        <!-- 步骤3: 完成 -->
        <view v-if="currentStep === 2" class="step-complete">
          <view class="success-icon">
            <text class="iconfont icon-check-circle"></text>
          </view>
          <text class="success-title">报告添加成功！</text>
          <text class="success-desc">您的健康报告已保存，可以在报告列表中查看</text>
          
          <view class="complete-actions">
            <custom-button 
              type="secondary" 
              @click="addAnother"
            >
              继续添加
            </custom-button>
            <custom-button 
              type="primary" 
              @click="viewReports"
            >
              查看报告
            </custom-button>
          </view>
        </view>
      </view>
      
      <!-- 底部操作按钮 -->
      <view v-if="currentStep < 2" class="bottom-actions">
        <custom-button 
          v-if="currentStep > 0"
          type="secondary" 
          @click="prevStep"
        >
          上一步
        </custom-button>
        
        <custom-button 
          type="primary" 
          :disabled="!canProceed"
          :loading="saving"
          @click="nextStep"
        >
          {{ currentStep === 1 ? '保存报告' : '下一步' }}
        </custom-button>
      </view>
    </view>
  </view>
</template>

<script>
import { useReportStore } from '@/stores/report'
import { generateId } from '@/utils/common'
import CameraUpload from '@/components/business/camera-upload.vue'
import CustomButton from '@/components/common/custom-button.vue'
import CustomInput from '@/components/common/custom-input.vue'

export default {
  name: 'AddReportPage',
  components: {
    CameraUpload,
    CustomButton,
    CustomInput
  },
  
  data() {
    return {
      currentStep: 0,
      saving: false,
      steps: [
        { key: 'upload', title: '上传图片' },
        { key: 'confirm', title: '确认信息' },
        { key: 'complete', title: '完成' }
      ],
      reportForm: {
        title: '',
        hospital: '',
        doctor: '',
        checkDate: '',
        reportDate: '',
        originalImage: '',
        items: [],
        notes: ''
      },
      categories: [
        '血常规', '生化检查', '免疫检查', '尿常规', 
        '心电图', '影像检查', '其他'
      ]
    }
  },
  
  computed: {
    canProceed() {
      if (this.currentStep === 0) {
        return !!this.reportForm.originalImage
      } else if (this.currentStep === 1) {
        return this.reportForm.title && 
               this.reportForm.hospital && 
               this.reportForm.checkDate &&
               this.reportForm.items.length > 0
      }
      return true
    }
  },
  
  methods: {
    // 处理图片选择
    handleImageSelected(imagePath) {
      this.reportForm.originalImage = imagePath
    },
    
    // 处理OCR成功
    handleOCRSuccess(result) {
      // 填充表单数据
      if (result.basicInfo) {
        this.reportForm.hospital = result.basicInfo.hospital || ''
        this.reportForm.doctor = result.basicInfo.doctor || ''
        this.reportForm.checkDate = result.basicInfo.checkDate || ''
        this.reportForm.reportDate = result.basicInfo.reportDate || ''
      }
      
      if (result.items) {
        this.reportForm.items = result.items.map(item => ({
          ...item,
          id: generateId()
        }))
      }
      
      // 自动生成标题
      if (!this.reportForm.title && this.reportForm.hospital) {
        const date = this.reportForm.checkDate || new Date().toISOString().split('T')[0]
        this.reportForm.title = `${this.reportForm.hospital} - ${date}`
      }
    },
    
    // 处理OCR完成
    handleOCRComplete() {
      // OCR完成后自动进入下一步
      if (this.reportForm.originalImage) {
        this.nextStep()
      }
    },
    
    // 手动输入
    goToManualInput() {
      this.currentStep = 1
      // 添加一个空的检查项目
      this.addHealthItem()
    },
    
    // 下一步
    async nextStep() {
      if (!this.canProceed) return
      
      if (this.currentStep === 1) {
        // 保存报告
        await this.saveReport()
      } else {
        this.currentStep++
      }
    },
    
    // 上一步
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    
    // 保存报告
    async saveReport() {
      try {
        this.saving = true
        
        const reportStore = useReportStore()
        await reportStore.addReport(this.reportForm)
        
        this.currentStep = 2
        
      } catch (error) {
        console.error('保存报告失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      } finally {
        this.saving = false
      }
    },
    
    // 添加检查项目
    addHealthItem() {
      this.reportForm.items.push({
        id: generateId(),
        name: '',
        value: '',
        unit: '',
        referenceRange: '',
        isAbnormal: false,
        category: '其他'
      })
    },
    
    // 移除检查项目
    removeHealthItem(index) {
      if (this.reportForm.items.length > 1) {
        this.reportForm.items.splice(index, 1)
      } else {
        uni.showToast({
          title: '至少保留一个检查项目',
          icon: 'none'
        })
      }
    },
    
    // 处理日期变化
    handleDateChange(field, event) {
      this.reportForm[field] = event.detail.value
    },
    
    // 处理分类变化
    handleCategoryChange(index, event) {
      const categoryIndex = event.detail.value
      this.reportForm.items[index].category = this.categories[categoryIndex]
    },
    
    // 获取分类索引
    getCategoryIndex(category) {
      return this.categories.indexOf(category) || 0
    },
    
    // 处理异常状态变化
    handleAbnormalChange(index, event) {
      this.reportForm.items[index].isAbnormal = event.detail.value
    },
    
    // 继续添加
    addAnother() {
      // 重置表单
      this.resetForm()
      this.currentStep = 0
    },
    
    // 查看报告
    viewReports() {
      uni.switchTab({
        url: '/pages/reports/list'
      })
    },
    
    // 重置表单
    resetForm() {
      this.reportForm = {
        title: '',
        hospital: '',
        doctor: '',
        checkDate: '',
        reportDate: '',
        originalImage: '',
        items: [],
        notes: ''
      }
      
      // 重置相机组件
      if (this.$refs.cameraUpload) {
        this.$refs.cameraUpload.reset()
      }
    }
  },
  
  onLoad() {
    // 设置默认报告日期为今天
    this.reportForm.reportDate = new Date().toISOString().split('T')[0]
  },
  
  onUnload() {
    // 页面卸载时清理临时数据
    this.resetForm()
  }
}
</script>

<style lang="scss" scoped>
.add-report-page {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: 160rpx;
}

.container {
  padding: 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.steps-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 48rpx;
  padding: 0 32rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 24rpx;
    left: 60%;
    right: -40%;
    height: 2rpx;
    background-color: var(--border-color);
    z-index: 1;
  }
  
  &.completed::after {
    background-color: var(--success-color);
  }
  
  .step-circle {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    background-color: var(--border-color);
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12rpx;
    position: relative;
    z-index: 2;
    
    .step-number {
      font-size: 24rpx;
      font-weight: 500;
    }
    
    .iconfont {
      font-size: 24rpx;
    }
  }
  
  &.active .step-circle {
    background-color: var(--primary-color);
  }
  
  &.completed .step-circle {
    background-color: var(--success-color);
  }
  
  .step-title {
    font-size: 24rpx;
    color: var(--text-secondary);
    text-align: center;
  }
  
  &.active .step-title {
    color: var(--primary-color);
    font-weight: 500;
  }
  
  &.completed .step-title {
    color: var(--success-color);
  }
}

.step-content {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: var(--shadow-color) 0 2rpx 8rpx;
  margin-bottom: 32rpx;
}

.step-desc {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 32rpx;
}

.form-section {
  margin-bottom: 48rpx;
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 24rpx;
  }
  
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
  }
}

.date-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.date-input-group {
  .input-label {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 16rpx;
  }
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--background-color);
  border: 2rpx solid var(--border-color);
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  min-height: 80rpx;
  
  .date-text {
    font-size: 28rpx;
    color: var(--text-color);
  }
  
  .iconfont {
    font-size: 28rpx;
    color: var(--text-secondary);
  }
}

.health-item-form {
  background-color: var(--background-color);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  
  .item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
    
    .item-index {
      font-size: 24rpx;
      color: var(--text-secondary);
      background-color: var(--primary-color);
      color: #ffffff;
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .item-delete {
      font-size: 32rpx;
      color: var(--error-color);
      padding: 8rpx;
    }
  }
  
  .item-inputs {
    .value-unit-group {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 16rpx;
    }
    
    .item-options {
      display: flex;
      align-items: center;
      gap: 24rpx;
      margin-top: 16rpx;
      
      .category-picker {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: var(--card-background);
        border: 2rpx solid var(--border-color);
        border-radius: 12rpx;
        padding: 16rpx 20rpx;
        
        .category-text {
          font-size: 26rpx;
          color: var(--text-color);
        }
        
        .iconfont {
          font-size: 24rpx;
          color: var(--text-secondary);
        }
      }
      
      .abnormal-switch {
        display: flex;
        align-items: center;
        gap: 12rpx;
        
        .switch-label {
          font-size: 26rpx;
          color: var(--text-color);
        }
      }
    }
  }
}

.step-complete {
  text-align: center;
  padding: 48rpx 0;
  
  .success-icon {
    margin-bottom: 32rpx;
    
    .iconfont {
      font-size: 128rpx;
      color: var(--success-color);
    }
  }
  
  .success-title {
    display: block;
    font-size: 36rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 16rpx;
  }
  
  .success-desc {
    display: block;
    font-size: 28rpx;
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 48rpx;
  }
  
  .complete-actions {
    display: flex;
    gap: 24rpx;
    justify-content: center;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--card-background);
  border-top: 1rpx solid var(--border-color);
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 24rpx;
  
  .custom-button {
    flex: 1;
  }
}
</style>
