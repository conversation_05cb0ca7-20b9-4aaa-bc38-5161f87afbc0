@echo off
echo ================================
echo 健康报告助手 - 项目启动脚本
echo ================================
echo.

echo 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo 检查npm环境...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到npm，请检查Node.js安装
    pause
    exit /b 1
)

echo.
echo 请选择运行模式:
echo 1. H5开发模式
echo 2. 微信小程序开发模式
echo 3. App开发模式
echo 4. 安装依赖
echo 5. 退出
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo 启动H5开发模式...
    npm run dev:h5
) else if "%choice%"=="2" (
    echo 启动微信小程序开发模式...
    npm run dev:mp-weixin
) else if "%choice%"=="3" (
    echo 启动App开发模式...
    npm run dev:app-plus
) else if "%choice%"=="4" (
    echo 安装项目依赖...
    npm install
    echo 依赖安装完成！
    pause
) else if "%choice%"=="5" (
    echo 退出脚本
    exit /b 0
) else (
    echo 无效选择，请重新运行脚本
    pause
)

pause
